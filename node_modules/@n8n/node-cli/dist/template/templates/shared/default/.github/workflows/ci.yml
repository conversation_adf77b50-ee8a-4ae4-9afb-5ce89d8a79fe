name: CI

on:
  pull_request:
  push:
    branches:
      - main

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'

      - name: Install dependencies
        run: '{{packageManager.name}} {{packageManager.installCommand}}'

      - name: Run lint
        run: '{{packageManager.name}} run lint'

      - name: Run build
        run: '{{packageManager.name}} run build'
