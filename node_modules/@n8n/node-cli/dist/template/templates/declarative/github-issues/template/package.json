{"name": "{{nodePackageName}}", "version": "0.1.0", "description": "n8n community node to work with the GitHub Issues API", "license": "MIT", "homepage": "https://example.com", "keywords": ["n8n-community-node-package"], "author": {"name": "{{user.name}}", "email": "{{user.email}}"}, "repository": {"type": "git", "url": ""}, "scripts": {"build": "n8n-node build", "build:watch": "tsc --watch", "dev": "n8n-node dev", "lint": "n8n-node lint", "lint:fix": "n8n-node lint --fix", "release": "n8n-node release", "prepublishOnly": "n8n-node prerelease"}, "files": ["dist"], "n8n": {"n8nNodesApiVersion": 1, "credentials": ["dist/credentials/GithubIssuesApi.credentials.js", "dist/credentials/GithubIssuesOAuth2Api.credentials.js"], "nodes": ["dist/nodes/GithubIssues/GithubIssues.node.js"]}, "devDependencies": {"@n8n/node-cli": "*", "eslint": "9.32.0", "prettier": "3.6.2", "release-it": "^19.0.4", "typescript": "5.9.2"}, "peerDependencies": {"n8n-workflow": "*"}}