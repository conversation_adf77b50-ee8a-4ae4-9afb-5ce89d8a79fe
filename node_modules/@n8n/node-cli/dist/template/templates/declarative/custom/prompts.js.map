{"version": 3, "file": "prompts.js", "sourceRoot": "", "sources": ["../../../../../src/template/templates/declarative/custom/prompts.ts"], "names": [], "mappings": ";;;AAAA,4CAA8C;AAG9C,uDAA8D;AAEvD,MAAM,oBAAoB,GAAG,KAAK,IAAI,EAAE,CAC9C,MAAM,IAAA,2BAAiB,EACtB,IAAA,gBAAM,EAAiB;IACtB,OAAO,EAAE,gDAAgD;IACzD,OAAO,EAAE;QACR;YACC,KAAK,EAAE,SAAS;YAChB,KAAK,EAAE,QAAQ;YACf,IAAI,EAAE,+CAA+C;SACrD;QACD;YACC,KAAK,EAAE,cAAc;YACrB,KAAK,EAAE,QAAQ;YACf,IAAI,EAAE,uEAAuE;SAC7E;QACD;YACC,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,QAAQ;YACf,IAAI,EAAE,0EAA0E;SAChF;QACD;YACC,KAAK,EAAE,YAAY;YACnB,KAAK,EAAE,WAAW;YAClB,IAAI,EAAE,2EAA2E;SACjF;QACD;YACC,KAAK,EAAE,QAAQ;YACf,KAAK,EAAE,QAAQ;YACf,IAAI,EAAE,wFAAwF;SAC9F;QACD;YACC,KAAK,EAAE,MAAM;YACb,KAAK,EAAE,MAAM;YACb,IAAI,EAAE,0DAA0D;SAChE;KACD;IACD,YAAY,EAAE,QAAQ;CACtB,CAAC,CACF,CAAC;AAtCU,QAAA,oBAAoB,wBAsC9B;AAEI,MAAM,aAAa,GAAG,KAAK,IAAI,EAAE,CACvC,MAAM,IAAA,2BAAiB,EACtB,IAAA,cAAI,EAAC;IACJ,OAAO,EAAE,iCAAiC;IAC1C,WAAW,EAAE,4BAA4B;IACzC,YAAY,EAAE,4BAA4B;IAC1C,QAAQ,EAAE,CAAC,KAAK,EAAE,EAAE;QACnB,IAAI,CAAC,KAAK;YAAE,OAAO;QAEnB,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;YACnE,OAAO,qCAAqC,CAAC;QAC9C,CAAC;QAED,IAAI,CAAC;YACJ,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC;QAChB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,OAAO,qBAAqB,CAAC;QAC9B,CAAC;QACD,OAAO;IACR,CAAC;CACD,CAAC,CACF,CAAC;AArBU,QAAA,aAAa,iBAqBvB;AAEI,MAAM,eAAe,GAAG,KAAK,IAAI,EAAE,CACzC,MAAM,IAAA,2BAAiB,EACtB,IAAA,gBAAM,EAA4C;IACjD,OAAO,EAAE,qCAAqC;IAC9C,OAAO,EAAE;QACR;YACC,KAAK,EAAE,oBAAoB;YAC3B,KAAK,EAAE,mBAAmB;YAC1B,IAAI,EAAE,sDAAsD;SAC5D;QACD;YACC,KAAK,EAAE,oBAAoB;YAC3B,KAAK,EAAE,mBAAmB;YAC1B,IAAI,EAAE,gDAAgD;SACtD;KACD;IACD,YAAY,EAAE,mBAAmB;CACjC,CAAC,CACF,CAAC;AAlBU,QAAA,eAAe,mBAkBzB"}