{"version": 3, "file": "template.js", "sourceRoot": "", "sources": ["../../../../../src/template/templates/declarative/custom/template.ts"], "names": [], "mappings": ";;;;;;AAAA,6CAAiE;AACjE,0DAA6B;AAE7B,+BAAgF;AAChF,uCAAiF;AAEjF,6DAIsC;AACtC,uDAImC;AACnC,wCAAkE;AAErD,QAAA,cAAc,GAAG,IAAA,qBAAc,EAAC;IAC5C,IAAI,EAAE,oBAAoB;IAC1B,WAAW,EAAE,kCAAkC;IAC/C,IAAI,EAAE,mBAAI,CAAC,IAAI,CAAC,SAAS,EAAE,UAAU,CAAC;IACtC,OAAO,EAAE,KAAK,IAAmC,EAAE;QAClD,MAAM,OAAO,GAAG,MAAM,IAAA,uBAAa,GAAE,CAAC;QAEtC,MAAM,cAAc,GAAG,MAAM,IAAA,8BAAoB,GAAE,CAAC;QAEpD,IAAI,cAAc,KAAK,QAAQ,EAAE,CAAC;YACjC,MAAM,IAAI,GAAG,MAAM,IAAA,yBAAe,GAAE,CAAC;YAErC,OAAO,EAAE,cAAc,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;QAC1C,CAAC;QAED,OAAO,EAAE,cAAc,EAAE,OAAO,EAAE,CAAC;IACpC,CAAC;IACD,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE;QACnB,MAAM,UAAU,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAClC,MAAM,aAAa,CAAC,IAAI,CAAC,CAAC;IAC3B,CAAC;CACD,CAAC,CAAC;AAEH,KAAK,UAAU,UAAU,CAAC,IAAwC,EAAE,WAAmB;IACtF,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC;IACpE,MAAM,YAAY,GAAG,IAAA,wBAAU,EAAC,QAAQ,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,CAAC;IACpE,MAAM,UAAU,GAAG,mBAAI,CAAC,OAAO,CAAC,eAAe,EAAE,SAAS,WAAW,EAAE,CAAC,CAAC;IAEzE,MAAM,IAAA,mCAAsB,EAAC,UAAU,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IACpE,MAAM,UAAU,GAAG,MAAM,IAAA,4BAAe,EAAC,UAAU,EAAE,YAAY,CAAC,CAAC;IAEnE,MAAM,WAAW,GAAG,mBAAI,CAAC,OAAO,CAAC,UAAU,EAAE,GAAG,YAAY,UAAU,CAAC,CAAC;IACxE,MAAM,UAAU,GAAG,IAAA,mBAAa,EAAC;QAChC,QAAQ,EAAE,WAAW;QACrB,OAAO,EAAE,MAAM,CAAC,OAAO;QACvB,SAAS,EAAE,YAAY;KACvB,CAAC,CAAC;IACH,MAAM,IAAA,0BAAa,EAAC,WAAW,EAAE,UAAU,CAAC,WAAW,EAAE,CAAC,CAAC;IAE3D,MAAM,KAAK,GAAG,CAAC,cAAc,YAAY,IAAI,YAAY,UAAU,CAAC,CAAC;IACrE,MAAM,IAAA,6BAAmB,EAAC,eAAe,EAAE,KAAK,CAAC,CAAC;AACnD,CAAC;AAED,KAAK,UAAU,aAAa,CAAC,IAAwC;IACpE,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,GAAG,IAAI,CAAC;IAC1D,IAAI,MAAM,CAAC,cAAc,KAAK,MAAM;QAAE,OAAO;IAE7C,MAAM,sBAAsB,GAC3B,MAAM,CAAC,cAAc,KAAK,QAAQ;QACjC,CAAC,CAAC,MAAM,CAAC,cAAc,GAAG,IAAA,wBAAU,EAAC,MAAM,CAAC,IAAI,CAAC;QACjD,CAAC,CAAC,MAAM,CAAC,cAAc,CAAC;IAC1B,MAAM,sBAAsB,GAAG,mBAAI,CAAC,OAAO,CAC1C,SAAS,EACT,4BAA4B,sBAAsB,iBAAiB,CACnE,CAAC;IAEF,MAAM,QAAQ,GAAG,eAAe,CAAC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;IAC1D,MAAM,QAAQ,GAAG,QAAQ,CAAC;IAC1B,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,MAAM,CAAC;IAC3C,MAAM,mBAAmB,GACxB,MAAM,CAAC,cAAc,KAAK,QAAQ;QACjC,CAAC,CAAC,IAAA,wBAAU,EAAC,GAAG,QAAQ,aAAa,CAAC;QACtC,CAAC,CAAC,IAAA,wBAAU,EAAC,GAAG,QAAQ,MAAM,CAAC,CAAC;IAClC,MAAM,cAAc,GAAG,IAAA,uBAAS,EAC/B,GAAG,QAAQ,GAAG,cAAc,KAAK,QAAQ,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,KAAK,EAAE,CACjE,CAAC;IACF,MAAM,qBAAqB,GAAG,GAAG,IAAA,yBAAW,EAAC,QAAQ,CAAC,IACrD,cAAc,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,KAC9C,EAAE,CAAC;IAEH,MAAM,oBAAoB,GAAG,IAAA,yBAAmB,EAAC;QAChD,QAAQ;QACR,OAAO;QACP,cAAc;QACd,qBAAqB;QACrB,mBAAmB;QACnB,cAAc,EAAE,sBAAsB;KACtC,CAAC,CAAC;IAEH,MAAM,IAAA,0BAAa,EAClB,mBAAI,CAAC,OAAO,CAAC,eAAe,EAAE,eAAe,mBAAmB,iBAAiB,CAAC,EAClF,oBAAoB,CAAC,WAAW,EAAE,CAClC,CAAC;IAEF,MAAM,IAAA,kCAAwB,EAC7B,eAAe,EACf,oBAAoB,mBAAmB,iBAAiB,CACxD,CAAC;IAEF,KAAK,MAAM,QAAQ,IAAI,MAAM,IAAA,6BAAmB,EAAC,eAAe,CAAC,EAAE,CAAC;QACnE,MAAM,WAAW,GAAG,mBAAI,CAAC,OAAO,CAC/B,eAAe,EACf,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CACtD,CAAC;QAEF,MAAM,cAAc,GAAG,IAAA,yBAAmB,EAAC;YAC1C,QAAQ,EAAE,WAAW;YACrB,cAAc;SACd,CAAC,CAAC;QAEH,MAAM,IAAA,0BAAa,EAAC,WAAW,EAAE,cAAc,CAAC,WAAW,EAAE,CAAC,CAAC;IAChE,CAAC;AACF,CAAC"}