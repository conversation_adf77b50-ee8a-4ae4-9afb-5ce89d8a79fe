{"version": 3, "file": "ast.js", "sourceRoot": "", "sources": ["../../../../../src/template/templates/declarative/custom/ast.ts"], "names": [], "mappings": ";;AASA,sCA4DC;AAED,kDAwDC;AAED,kDA+BC;AAhKD,6CAAqD;AACrD,uCAAqD;AAErD,+CAI+B;AAE/B,SAAgB,aAAa,CAAC,EAC7B,QAAQ,EACR,SAAS,EACT,OAAO,GACmD;IAC1D,MAAM,UAAU,GAAG,IAAA,0BAAoB,EAAC,QAAQ,CAAC,CAAC;IAClD,MAAM,SAAS,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;IAE7C,SAAS,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC;IAC5B,MAAM,kBAAkB,GAAG,SAAS;SAClC,kBAAkB,CAAC,aAAa,CAAC;SACjC,2BAA2B,CAAC,qBAAU,CAAC,uBAAuB,CAAC,CAAC;IAElE,IAAA,0BAAoB,EAAC;QACpB,GAAG,EAAE,kBAAkB;QACvB,GAAG,EAAE,aAAa;QAClB,KAAK,EAAE,IAAA,yBAAW,EAAC,SAAS,CAAC;KAC7B,CAAC,CAAC;IACH,IAAA,0BAAoB,EAAC;QACpB,GAAG,EAAE,kBAAkB;QACvB,GAAG,EAAE,MAAM;QACX,KAAK,EAAE,IAAA,uBAAS,EAAC,SAAS,CAAC;KAC3B,CAAC,CAAC;IACH,IAAA,0BAAoB,EAAC;QACpB,GAAG,EAAE,kBAAkB;QACvB,GAAG,EAAE,aAAa;QAClB,KAAK,EAAE,qBAAqB,IAAA,yBAAW,EAAC,SAAS,CAAC,MAAM;KACxD,CAAC,CAAC;IAEH,MAAM,IAAI,GAAG,IAAA,2BAAqB,EAAC,EAAE,GAAG,EAAE,kBAAkB,EAAE,GAAG,EAAE,MAAM,EAAE,CAAC,CAAC;IAC7E,IAAA,0BAAoB,EAAC;QACpB,GAAG,EAAE,IAAI;QACT,GAAG,EAAE,OAAO;QACZ,KAAK,EAAE,QAAQ,IAAA,uBAAS,EAAC,SAAS,CAAC,MAAM;KACzC,CAAC,CAAC;IACH,IAAA,0BAAoB,EAAC;QACpB,GAAG,EAAE,IAAI;QACT,GAAG,EAAE,MAAM;QACX,KAAK,EAAE,QAAQ,IAAA,uBAAS,EAAC,SAAS,CAAC,WAAW;KAC9C,CAAC,CAAC;IAEH,MAAM,eAAe,GAAG,IAAA,2BAAqB,EAAC;QAC7C,GAAG,EAAE,kBAAkB;QACvB,GAAG,EAAE,iBAAiB;KACtB,CAAC,CAAC;IAEH,IAAA,0BAAoB,EAAC;QACpB,GAAG,EAAE,eAAe;QACpB,GAAG,EAAE,SAAS;QACd,KAAK,EAAE,OAAO;KACd,CAAC,CAAC;IAEH,MAAM,QAAQ,GAAG,IAAA,2BAAqB,EAAC;QACtC,GAAG,EAAE,kBAAkB;QACvB,GAAG,EAAE,UAAU;KACf,CAAC,CAAC;IAEH,IAAA,0BAAoB,EAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,IAAA,yBAAW,EAAC,SAAS,CAAC,EAAE,CAAC,CAAC;IAEpF,OAAO,UAAU,CAAC;AACnB,CAAC;AAED,SAAgB,mBAAmB,CAAC,EACnC,QAAQ,EACR,OAAO,EACP,cAAc,EACd,cAAc,EACd,qBAAqB,EACrB,mBAAmB,GAQnB;IACA,MAAM,UAAU,GAAG,IAAA,0BAAoB,EAAC,cAAc,CAAC,CAAC;IACxD,MAAM,SAAS,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;IAE7C,SAAS,CAAC,MAAM,CAAC,mBAAmB,CAAC,CAAC;IAEtC,IAAA,0BAAoB,EAAC;QACpB,GAAG,EAAE,SAAS;QACd,GAAG,EAAE,aAAa;QAClB,KAAK,EAAE,qBAAqB;KAC5B,CAAC,CAAC;IAEH,IAAA,0BAAoB,EAAC;QACpB,GAAG,EAAE,SAAS;QACd,GAAG,EAAE,MAAM;QACX,KAAK,EAAE,cAAc;KACrB,CAAC,CAAC;IAEH,MAAM,UAAU,GAAG,SAAS,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;IAC7D,IAAI,UAAU,EAAE,CAAC;QAChB,MAAM,WAAW,GAAG,UAAU,CAAC,2BAA2B,CAAC,qBAAU,CAAC,aAAa,CAAC,CAAC;QACrF,MAAM,MAAM,GAAG,WAAW,CAAC,cAAc,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,QAAQ,EAAE,CAAC,CAAC;QAC7E,WAAW,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IACrC,CAAC;IAED,MAAM,YAAY,GAAG,SAAS,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;IAEnD,IAAI,YAAY,EAAE,CAAC;QAClB,MAAM,WAAW,GAAG,YAAY;aAC9B,2BAA2B,CAAC,qBAAU,CAAC,uBAAuB,CAAC;aAC/D,kBAAkB,CAAC,SAAS,CAAC;aAC7B,aAAa,CAAC,qBAAU,CAAC,kBAAkB,CAAC;aAC5C,2BAA2B,CAAC,qBAAU,CAAC,uBAAuB,CAAC,CAAC;QAElE,IAAA,0BAAoB,EAAC;YACpB,GAAG,EAAE,WAAW;YAChB,GAAG,EAAE,SAAS;YACd,KAAK,EAAE,OAAO;SACd,CAAC,CAAC;IACJ,CAAC;IAED,OAAO,UAAU,CAAC;AACnB,CAAC;AAED,SAAgB,mBAAmB,CAAC,EACnC,QAAQ,EACR,cAAc,GACgC;IAC9C,MAAM,UAAU,GAAG,IAAA,0BAAoB,EAAC,QAAQ,CAAC,CAAC;IAClD,MAAM,SAAS,GAAG,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC;IAE7C,MAAM,eAAe,GAAG,SAAS;SAC/B,kBAAkB,CAAC,aAAa,CAAC;SACjC,2BAA2B,CAAC,qBAAU,CAAC,uBAAuB,CAAC,CAAC;IAElE,MAAM,eAAe,GAAG,eAAe,CAAC,kBAAkB,CAAC,aAAa,CAAC,CAAC;IAE1E,IAAI,eAAe,CAAC,OAAO,EAAE,KAAK,qBAAU,CAAC,kBAAkB,EAAE,CAAC;QACjE,MAAM,WAAW,GAAG,eAAe,CAAC,+BAA+B,CAClE,qBAAU,CAAC,sBAAsB,CACjC,CAAC;QACF,MAAM,gBAAgB,GAAG,aAAE,CAAC,OAAO,CAAC,6BAA6B,CAAC;YACjE,aAAE,CAAC,OAAO,CAAC,wBAAwB,CAClC,aAAE,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,CAAC,EACnC,aAAE,CAAC,OAAO,CAAC,mBAAmB,CAAC,cAAc,EAAE,IAAI,CAAC,CACpD;YACD,aAAE,CAAC,OAAO,CAAC,wBAAwB,CAClC,aAAE,CAAC,OAAO,CAAC,gBAAgB,CAAC,UAAU,CAAC,EACvC,aAAE,CAAC,OAAO,CAAC,UAAU,EAAE,CACvB;SACD,CAAC,CAAC;QACH,WAAW,CAAC,UAAU,CAAC,IAAA,oBAAS,EAAC,gBAAgB,CAAC,CAAC,CAAC;IACrD,CAAC;IAED,OAAO,UAAU,CAAC;AACnB,CAAC"}