export declare function updateNodeAst({ nodePath, className, baseUrl, }: {
    nodePath: string;
    className: string;
    baseUrl: string;
}): import("ts-morph").SourceFile;
export declare function updateCredentialAst({ repoName, baseUrl, credentialPath, credentialName, credentialDisplayName, credentialClassName, }: {
    repoName: string;
    credentialPath: string;
    credentialName: string;
    credentialDisplayName: string;
    credentialClassName: string;
    baseUrl: string;
}): import("ts-morph").SourceFile;
export declare function addCredentialToNode({ nodePath, credentialName, }: {
    nodePath: string;
    credentialName: string;
}): import("ts-morph").SourceFile;
