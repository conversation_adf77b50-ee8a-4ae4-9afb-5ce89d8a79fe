{"version": 3, "file": "build.js", "sourceRoot": "", "sources": ["../../src/commands/build.ts"], "names": [], "mappings": ";;;;;AAsDA,0CAYC;AAlED,4CAAoE;AACpE,sCAAsC;AACtC,0DAA6B;AAC7B,+CAA6C;AAC7C,0DAA6B;AAC7B,4DAAoC;AACpC,mCAAgC;AAEhC,0DAAoD;AACpD,8CAAoD;AAEpD,MAAqB,KAAM,SAAQ,cAAO;IAKzC,KAAK,CAAC,GAAG;QACR,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QAExB,MAAM,WAAW,GAAG,gBAAgB,CAAC;QACrC,IAAA,eAAK,EAAC,oBAAU,CAAC,OAAO,CAAC,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC;QAE9C,MAAM,IAAA,0BAAgB,EAAC,WAAW,CAAC,CAAC;QAEpC,MAAM,YAAY,GAAG,IAAA,iBAAO,GAAE,CAAC;QAC/B,YAAY,CAAC,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAChD,MAAM,IAAA,eAAM,EAAC,MAAM,CAAC,CAAC;QAErB,IAAI,CAAC;YACJ,MAAM,WAAW,EAAE,CAAC;YACpB,YAAY,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAClD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,IAAA,gBAAM,EAAC,yBAAyB,CAAC,CAAC;YAClC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACd,CAAC;QAED,MAAM,sBAAsB,GAAG,IAAA,iBAAO,GAAE,CAAC;QACzC,sBAAsB,CAAC,KAAK,CAAC,sBAAsB,CAAC,CAAC;QACrD,MAAM,eAAe,EAAE,CAAC;QACxB,sBAAsB,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;QAEnD,IAAA,eAAK,EAAC,oBAAoB,CAAC,CAAC;IAC7B,CAAC;;AA9Be,iBAAW,GAAG,2DAA2D,CAAC;AAC1E,cAAQ,GAAG,CAAC,qCAAqC,CAAC,CAAC;AACnD,WAAK,GAAG,EAAE,CAAC;kBAHP,KAAK;AAkC1B,KAAK,UAAU,WAAW;IACzB,OAAO,MAAM,IAAA,0BAAU,EAAC,KAAK,EAAE,EAAE,EAAE;QAClC,OAAO,EAAE,OAAO;QAChB,WAAW,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE;YACnC,aAAG,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;QAC7C,CAAC;KACD,CAAC,CAAC;AACJ,CAAC;AAEM,KAAK,UAAU,eAAe;IACpC,MAAM,WAAW,GAAG,mBAAI,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,yBAAyB,CAAC,EAAE;QAC5E,MAAM,EAAE,CAAC,MAAM,EAAE,cAAc,CAAC;KAChC,CAAC,CAAC;IAEH,OAAO,MAAM,OAAO,CAAC,GAAG,CACvB,WAAW,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,EAAE,EAAE;QAClC,MAAM,QAAQ,GAAG,mBAAI,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;QAC7C,MAAM,IAAA,gBAAK,EAAC,mBAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QACzD,OAAO,MAAM,IAAA,aAAE,EAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1D,CAAC,CAAC,CACF,CAAC;AACH,CAAC"}