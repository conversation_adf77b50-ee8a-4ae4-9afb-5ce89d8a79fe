{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/commands/dev/index.ts"], "names": [], "mappings": ";;;;;AAAA,4CAA4D;AAC5D,sCAA6C;AAC7C,sDAAyB;AACzB,0DAA6B;AAC7B,4DAAoC;AACpC,mCAAgC;AAEhC,uDAAsD;AACtD,iEAAmE;AACnE,iDAAiE;AACjE,uDAA0D;AAC1D,oCAA2C;AAC3C,mCAAoD;AACpD,6DAAuD;AAEvD,MAAqB,GAAI,SAAQ,cAAO;IAoBvC,KAAK,CAAC,GAAG;QACR,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAExC,MAAM,cAAc,GAAG,CAAC,MAAM,IAAA,sCAAoB,GAAE,CAAC,IAAI,KAAK,CAAC;QAC/D,MAAM,EAAE,oBAAoB,EAAE,GAAG,IAAA,gBAAQ,GAAE,CAAC;QAE5C,IAAA,eAAK,EAAC,oBAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC,CAAC;QAE5C,MAAM,IAAA,0BAAgB,EAAC,cAAc,CAAC,CAAC;QAEvC,MAAM,IAAA,uBAAe,GAAE,CAAC;QAExB,MAAM,cAAc,GAAG,IAAA,iBAAO,GAAE,CAAC;QACjC,cAAc,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;QACnD,MAAM,IAAA,0BAAU,EAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC;QAE3C,MAAM,aAAa,GAAG,KAAK,CAAC,oBAAoB,CAAC,CAAC;QAClD,MAAM,iBAAiB,GAAG,mBAAI,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;QAErE,MAAM,IAAA,yBAAY,EAAC,iBAAiB,CAAC,CAAC;QAEtC,MAAM,WAAW,GAAG,MAAM,IAAA,uBAAe,GAAE,CAAC;QAC5C,MAAM,oBAAoB,GAAG,IAAA,6BAAgB,EAAC,WAAW,CAAC,CAAC;QAE3D,IAAI,oBAAoB;YAAE,OAAO,IAAA,kBAAQ,EAAC,oBAAoB,CAAC,CAAC;QAGhE,MAAM,IAAA,eAAM,EAAC,mBAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE,cAAc,CAAC,CAAC,CAAC;QAC3D,MAAM,IAAA,0BAAU,EAAC,cAAc,EAAE,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE;YACvD,GAAG,EAAE,iBAAiB;SACtB,CAAC,CAAC;QAEH,cAAc,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;QAEjD,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC;YAC5B,IAAI,aAAa,GAAG,KAAK,CAAC;YAC1B,MAAM,aAAa,GAAG,IAAA,iBAAO,GAAE,CAAC;YAChC,aAAa,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAC/C,aAAG,CAAC,IAAI,CAAC,oBAAU,CAAC,GAAG,CAAC,mEAAmE,CAAC,CAAC,CAAC;YAG9F,IAAI,CAAC;gBACJ,MAAM,OAAO,CAAC,IAAI,CAAC;oBAClB,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,EAAE;wBAC7B,oBAAoB,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,SAAS,EAAE,iBAAiB,EAAE,YAAY,CAAC,EAAE;4BAC/E,GAAG,EAAE,aAAa;4BAClB,GAAG,EAAE;gCACJ,GAAG,OAAO,CAAC,GAAG;gCACd,cAAc,EAAE,MAAM;gCACtB,mBAAmB,EAAE,MAAM;gCAC3B,mBAAmB,EAAE,IAAI;gCACzB,eAAe,EAAE,aAAa;6BAC9B;4BACD,IAAI,EAAE,KAAK;4BACX,KAAK,EAAE,oBAAU,CAAC,KAAK;4BACvB,WAAW,EAAE,CAAC,IAAI,EAAE,EAAE;gCACrB,IAAI,IAAI,CAAC,QAAQ,CAAC,0BAA0B,CAAC,EAAE,CAAC;oCAC/C,OAAO,EAAE,CAAC;gCACX,CAAC;gCAED,OAAO,aAAa,CAAC;4BACtB,CAAC;yBACD,CAAC,CAAC;oBACJ,CAAC,CAAC;oBACF,IAAI,OAAO,CAAO,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;wBAC/B,UAAU,CAAC,GAAG,EAAE;4BACf,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;4BACjE,MAAM,CAAC,KAAK,CAAC,CAAC;wBACf,CAAC,EAAE,OAAO,CAAC,CAAC;oBACb,CAAC,CAAC;iBACF,CAAC,CAAC;gBAEH,aAAa,GAAG,IAAI,CAAC;gBACrB,aAAa,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;YAC9C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBAChB,aAAa,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAC;gBACrD,IAAA,kBAAQ,EAAC,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,wBAAwB,EAAE,CAAC,CAAC,CAAC;gBAC/E,OAAO;YACR,CAAC;QACF,CAAC;QAED,IAAA,eAAK,EAAC,kBAAkB,CAAC,CAAC;QAG1B,oBAAoB,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,CAAC,EAAE;YACtE,IAAI,EAAE,OAAO;YACb,KAAK,EAAE,oBAAU,CAAC,IAAI;SACtB,CAAC,CAAC;IACJ,CAAC;;AA3Ge,eAAW,GAAG,+DAA+D,CAAC;AAC9E,YAAQ,GAAG;IAC1B,qCAAqC;IACrC,oDAAoD;IACpD,sEAAsE;CACtE,CAAC;AACc,SAAK,GAAG;IACvB,cAAc,EAAE,YAAK,CAAC,OAAO,CAAC;QAC7B,OAAO,EAAE,KAAK;QACd,WAAW,EACV,yKAAyK;KAC1K,CAAC;IACF,oBAAoB,EAAE,YAAK,CAAC,SAAS,CAAC;QACrC,OAAO,EAAE,mBAAI,CAAC,IAAI,CAAC,iBAAE,CAAC,OAAO,EAAE,EAAE,eAAe,CAAC;QACjD,WAAW,EACV,kIAAkI;KACnI,CAAC;CACF,CAAC;kBAlBkB,GAAG"}