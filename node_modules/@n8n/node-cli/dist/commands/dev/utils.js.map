{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../src/commands/dev/utils.ts"], "names": [], "mappings": ";;;;;AAOA,4BAqIC;AAED,0CAIC;AAjJD,iDAAyD;AACzD,gEAAkC;AAGlC,2CAA6C;AAE7C,SAAgB,QAAQ;IACvB,MAAM,cAAc,GAAmB,EAAE,CAAC;IAC1C,IAAI,cAAc,GAAG,KAAK,CAAC;IAE3B,MAAM,aAAa,GAAG,CAAC,KAAmB,EAAQ,EAAE;QACnD,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;IAC5B,CAAC,CAAC;IAEF,MAAM,SAAS,GAAG,CAAC,KAAmB,EAAE,MAAsB,EAAQ,EAAE;QACvE,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACnB,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpB,CAAC;IACF,CAAC,CAAC;IAEF,MAAM,oBAAoB,GAAG,GAAS,EAAE;QACvC,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC;QAC/D,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IACjB,CAAC,CAAC;IAEF,MAAM,gBAAgB,GAAG,CAAC,MAA4B,EAAQ,EAAE;QAC/D,IAAI,cAAc,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YACjC,OAAO,CAAC,IAAI,EAAE,CAAC;YACf,OAAO;QACR,CAAC;QAED,IAAI,WAAW,GAAG,CAAC,CAAC;QACpB,MAAM,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC;QAE5C,MAAM,cAAc,GAAG,UAAU,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;QAE9D,MAAM,WAAW,GAAG,GAAG,EAAE;YACxB,WAAW,EAAE,CAAC;YACd,IAAI,WAAW,KAAK,aAAa,EAAE,CAAC;gBACnC,YAAY,CAAC,cAAc,CAAC,CAAC;gBAC7B,OAAO,CAAC,IAAI,EAAE,CAAC;YAChB,CAAC;QACF,CAAC,CAAC;QAEF,cAAc,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,EAAE;YAChC,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;gBACnB,KAAK,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;gBAChC,SAAS,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;gBAGzB,UAAU,CAAC,GAAG,EAAE,CAAC,SAAS,CAAC,KAAK,EAAE,SAAS,CAAC,EAAE,IAAI,CAAC,CAAC;YACrD,CAAC;iBAAM,CAAC;gBACP,WAAW,EAAE,CAAC;YACf,CAAC;QACF,CAAC,CAAC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM,YAAY,GAAG,CAAC,MAA4B,EAAQ,EAAE;QAC3D,IAAI,cAAc,EAAE,CAAC;YAEpB,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;YAC5C,oBAAoB,EAAE,CAAC;YACvB,OAAO;QACR,CAAC;QAED,cAAc,GAAG,IAAI,CAAC;QACtB,IAAI,MAAM,KAAK,QAAQ,EAAE,CAAC;YACzB,OAAO,CAAC,GAAG,CAAC,kEAAkE,CAAC,CAAC;QACjF,CAAC;QACD,gBAAgB,CAAC,MAAM,CAAC,CAAC;IAC1B,CAAC,CAAC;IAEF,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC,CAAC;IACnD,OAAO,CAAC,EAAE,CAAC,SAAS,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,SAAS,CAAC,CAAC,CAAC;IAErD,MAAM,cAAc,GAAG,CAAC,KAAa,EAAU,EAAE,CAChD,KAAK;SACH,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;SACrB,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;SACxB,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC;SACxB,OAAO,CAAC,UAAU,EAAE,EAAE,CAAC;SACvB,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC;IAE7B,MAAM,YAAY,GACjB,CAAC,IAAa,EAAE,KAAiB,EAAE,WAAuC,EAAE,EAAE,CAC9E,CAAC,IAAY,EAAQ,EAAE;QACtB,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YAAE,OAAO;QAE9C,MAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACtE,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,MAAM,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;IAEH,MAAM,aAAa,GAAG,CAAC,IAAY,EAAE,MAA8B,EAAQ,EAAE;QAC5E,IAAI;aACF,QAAQ,EAAE;aACV,KAAK,CAAC,IAAI,CAAC;aACX,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC;aAC1C,MAAM,CAAC,OAAO,CAAC;aACf,OAAO,CAAC,MAAM,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,MAAM,oBAAoB,GAAG,CAC5B,GAAW,EACX,IAAc,EACd,OAMI,EAAE,EACS,EAAE;QACjB,MAAM,KAAK,GAAG,IAAA,qBAAK,EAAC,GAAG,EAAE,IAAI,EAAE;YAC9B,GAAG,EAAE,IAAI,CAAC,GAAG;YACb,GAAG,EAAE,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,GAAG,EAAE;YACpC,KAAK,EAAE,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC;SAClC,CAAC,CAAC;QAEH,aAAa,CAAC,KAAK,CAAC,CAAC;QAErB,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QACrE,MAAM,YAAY,GAAG,CAAC,IAAY,EAAE,EAAE,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;QAEnE,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QACvC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAEvC,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,IAAI,EAAE,EAAE;YAC1B,IAAI,CAAC,cAAc,EAAE,CAAC;gBACrB,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,IAAI,GAAG,qBAAqB,IAAI,EAAE,CAAC,CAAC;gBAC5D,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;YACzB,CAAC;QACF,CAAC,CAAC,CAAC;QAEH,OAAO,KAAK,CAAC;IACd,CAAC,CAAC;IAEF,OAAO;QACN,oBAAoB;KACpB,CAAC;AACH,CAAC;AAEM,KAAK,UAAU,eAAe;IACpC,OAAO,MAAM,kBAAE;SACb,QAAQ,CAAC,cAAc,EAAE,OAAO,CAAC;SACjC,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC,IAAA,gBAAS,EAAmB,WAAW,CAAC,EAAE,IAAI,IAAI,SAAS,CAAC,CAAC;AACtF,CAAC"}