{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../src/commands/new/index.ts"], "names": [], "mappings": ";;;;;AAAA,4CAAqF;AACrF,sCAAmD;AACnD,6CAAwC;AACxC,gEAAkC;AAClC,0DAA6B;AAC7B,4DAAoC;AAEpC,uCAAsF;AACtF,mCAAsC;AAEtC,wDAAkG;AAClG,6DAA0E;AAC1E,uDAAoE;AACpE,yCAA0D;AAC1D,iEAAmE;AACnE,iDAA+C;AAC/C,uDAA0D;AAE1D,MAAqB,GAAI,SAAQ,cAAO;IAsBvC,KAAK,CAAC,GAAG;QACR,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAC9C,MAAM,CAAC,QAAQ,EAAE,YAAY,CAAC,GAAG,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAElE,IAAA,eAAK,EAAC,oBAAU,CAAC,OAAO,CAAC,IAAA,mBAAW,GAAE,CAAC,CAAC,CAAC;QAEzC,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,IAAA,wBAAc,GAAE,CAAC,CAAC;QACvD,MAAM,oBAAoB,GAAG,IAAA,6BAAgB,EAAC,QAAQ,CAAC,CAAC;QAExD,IAAI,oBAAoB;YAAE,OAAO,IAAA,kBAAQ,EAAC,oBAAoB,CAAC,CAAC;QAEhE,MAAM,WAAW,GAAG,mBAAI,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,QAAQ,CAAC,CAAC;QAE1D,IAAI,SAAS,GAAG,KAAK,CAAC;QACtB,IAAI,MAAM,IAAA,yBAAY,EAAC,WAAW,CAAC,EAAE,CAAC;YACrC,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;gBAClB,MAAM,eAAe,GAAG,MAAM,IAAA,iBAAO,EAAC;oBACrC,OAAO,EAAE,KAAK,QAAQ,4CAA4C;iBAClE,CAAC,CAAC;gBACH,IAAI,IAAA,kBAAQ,EAAC,eAAe,CAAC,IAAI,CAAC,eAAe;oBAAE,OAAO,IAAA,kBAAQ,GAAE,CAAC;YACtE,CAAC;YAED,SAAS,GAAG,IAAI,CAAC;QAClB,CAAC;QAED,MAAM,IAAI,GAAG,QAAQ,IAAI,CAAC,MAAM,IAAA,wBAAc,GAAE,CAAC,CAAC;QAClD,IAAI,CAAC,IAAA,0BAAc,EAAC,IAAI,CAAC,EAAE,CAAC;YAC3B,OAAO,IAAA,kBAAQ,EAAC,0BAA0B,IAAI,EAAE,CAAC,CAAC;QACnD,CAAC;QAED,IAAI,QAAQ,GAAoB,qBAAS,CAAC,YAAY,CAAC,OAAO,CAAC;QAC/D,IAAI,YAAY,EAAE,CAAC;YAClB,MAAM,IAAI,GAAG,IAAA,uBAAS,EAAC,YAAY,CAAC,CAAC;YACrC,IAAI,CAAC,IAAA,0BAAc,EAAC,IAAI,EAAE,IAAI,CAAC,EAAE,CAAC;gBACjC,OAAO,IAAA,kBAAQ,EAAC,0BAA0B,IAAI,cAAc,IAAI,EAAE,CAAC,CAAC;YACrE,CAAC;YACD,QAAQ,GAAG,IAAA,uBAAW,EAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACpC,CAAC;aAAM,IAAI,IAAI,KAAK,aAAa,EAAE,CAAC;YACnC,MAAM,cAAc,GAAG,MAAM,IAAA,mCAAyB,GAAE,CAAC;YACzD,QAAQ,GAAG,IAAA,uBAAW,EAAC,aAAa,EAAE,cAAc,CAAoB,CAAC;QAC1E,CAAC;QAED,MAAM,MAAM,GAAG,CAAC,MAAM,QAAQ,CAAC,OAAO,EAAE,EAAE,CAAC,IAAI,EAAE,CAAC;QAClD,MAAM,cAAc,GAAG,CAAC,MAAM,IAAA,sCAAoB,GAAE,CAAC,IAAI,KAAK,CAAC;QAC/D,MAAM,YAAY,GAAiB;YAClC,eAAe,EAAE,WAAW;YAC5B,eAAe,EAAE,QAAQ;YACzB,MAAM;YACN,IAAI,EAAE,IAAA,oBAAc,GAAE;YACtB,cAAc,EAAE;gBACf,IAAI,EAAE,cAAc;gBACpB,cAAc,EAAE,cAAc,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,SAAS;aAC3D;SACD,CAAC;QACF,MAAM,cAAc,GAAG,IAAA,iBAAO,GAAE,CAAC;QACjC,cAAc,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC;QACtC,IAAI,SAAS,EAAE,CAAC;YACf,MAAM,kBAAE,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC;QAC5D,CAAC;QACD,MAAM,IAAA,yBAAY,EAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,EAAE,IAAI,CAAC,CAAC;QACrD,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QAEpC,MAAM,UAAU,GAAG,IAAA,iBAAO,GAAE,CAAC;QAC7B,UAAU,CAAC,KAAK,CAAC,6BAA6B,CAAC,CAAC;QAEhD,IAAI,CAAC;YACJ,MAAM,IAAA,aAAO,EAAC,WAAW,CAAC,CAAC;YAE3B,UAAU,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;QAC/C,CAAC;QAAC,OAAO,KAAc,EAAE,CAAC;YACzB,IAAI,KAAK,YAAY,iCAAiB,EAAE,CAAC;gBACxC,UAAU,CAAC,IAAI,CACd,wCAAwC,KAAK,CAAC,OAAO,EAAE,EACvD,KAAK,CAAC,IAAI,IAAI,SAAS,CACvB,CAAC;gBACF,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;YAC/B,CAAC;iBAAM,CAAC;gBACP,MAAM,KAAK,CAAC;YACb,CAAC;QACF,CAAC;QAED,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE,CAAC;YAC5B,MAAM,iBAAiB,GAAG,IAAA,iBAAO,GAAE,CAAC;YACpC,iBAAiB,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAC;YAEnD,IAAI,CAAC;gBACJ,MAAM,IAAA,yBAAY,EACjB,IAAA,0BAAU,EAAC,cAAc,EAAE,CAAC,SAAS,CAAC,EAAE;oBACvC,GAAG,EAAE,WAAW;oBAChB,WAAW,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE;wBACnC,aAAG,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC;oBAC7C,CAAC;iBACD,CAAC,EACF,IAAI,CACJ,CAAC;YACH,CAAC;YAAC,OAAO,KAAc,EAAE,CAAC;gBACzB,IAAI,KAAK,YAAY,iCAAiB,EAAE,CAAC;oBACxC,iBAAiB,CAAC,IAAI,CACrB,mCAAmC,KAAK,CAAC,OAAO,EAAE,EAClD,KAAK,CAAC,IAAI,IAAI,SAAS,CACvB,CAAC;oBACF,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;gBAC/B,CAAC;qBAAM,CAAC;oBACP,MAAM,KAAK,CAAC;gBACb,CAAC;YACF,CAAC;YAED,iBAAiB,CAAC,IAAI,CAAC,wBAAwB,CAAC,CAAC;QAClD,CAAC;QAED,IAAA,cAAI,EACH,QAAQ,QAAQ,OAAO,cAAc;;0EAEkC,IAAI;uCACvC,EACpC,YAAY,CACZ,CAAC;QAEF,IAAA,eAAK,EAAC,aAAa,QAAQ,IAAI,CAAC,CAAC;IAClC,CAAC;;AA5Ie,eAAW,GAAG,oDAAoD,CAAC;AACnE,YAAQ,GAAG;IAC1B,qCAAqC;IACrC,qEAAqE;IACrE,8DAA8D;IAC9D,oFAAoF;CACpF,CAAC;AACc,QAAI,GAAG;IACtB,IAAI,EAAE,WAAI,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;CACnC,CAAC;AACc,SAAK,GAAG;IACvB,KAAK,EAAE,YAAK,CAAC,OAAO,CAAC;QACpB,IAAI,EAAE,GAAG;QACT,WAAW,EAAE,mDAAmD;KAChE,CAAC;IACF,cAAc,EAAE,YAAK,CAAC,OAAO,CAAC,EAAE,WAAW,EAAE,8BAA8B,EAAE,CAAC;IAC9E,QAAQ,EAAE,YAAK,CAAC,MAAM,CAAC;QACtB,OAAO,EAAE,CAAC,2BAA2B,EAAE,oBAAoB,EAAE,sBAAsB,CAAU;KAC7F,CAAC;CACF,CAAC;kBApBkB,GAAG"}