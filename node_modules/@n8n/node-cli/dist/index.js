"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.commands = void 0;
const build_1 = __importDefault(require("./commands/build"));
const dev_1 = __importDefault(require("./commands/dev"));
const lint_1 = __importDefault(require("./commands/lint"));
const new_1 = __importDefault(require("./commands/new"));
const prerelease_1 = __importDefault(require("./commands/prerelease"));
const release_1 = __importDefault(require("./commands/release"));
exports.commands = {
    new: new_1.default,
    build: build_1.default,
    dev: dev_1.default,
    prerelease: prerelease_1.default,
    release: release_1.default,
    lint: lint_1.default,
};
//# sourceMappingURL=index.js.map