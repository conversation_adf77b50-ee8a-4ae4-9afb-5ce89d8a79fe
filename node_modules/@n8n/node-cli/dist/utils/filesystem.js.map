{"version": 3, "file": "filesystem.js", "sourceRoot": "", "sources": ["../../src/utils/filesystem.ts"], "names": [], "mappings": ";;;;;AAIA,oCAOC;AAED,gCA6BC;AAED,oCAIC;AAED,sCAMC;AAED,oCAEC;AAED,wDAmBC;AAED,0CAKC;AAxFD,6CAAwC;AACxC,gEAAkC;AAClC,0DAA6B;AAEtB,KAAK,UAAU,YAAY,CAAC,GAAW;IAC7C,IAAI,CAAC;QACJ,MAAM,IAAI,GAAG,MAAM,kBAAE,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QAChC,OAAO,IAAI,CAAC,WAAW,EAAE,CAAC;IAC3B,CAAC;IAAC,OAAO,KAAc,EAAE,CAAC;QACzB,OAAO,KAAK,CAAC;IACd,CAAC;AACF,CAAC;AAEM,KAAK,UAAU,UAAU,CAAC,EAChC,MAAM,EAAE,MAAM,EACd,WAAW,EACX,MAAM,GAAG,EAAE,GACiD;IAC5D,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,MAAM,CAAC,CAAC;IAElC,KAAK,UAAU,WAAW,CAAC,UAAkB,EAAE,WAAmB;QACjE,MAAM,OAAO,GAAG,MAAM,kBAAE,CAAC,OAAO,CAAC,UAAU,EAAE,EAAE,aAAa,EAAE,IAAI,EAAE,CAAC,CAAC;QAEtE,MAAM,OAAO,CAAC,GAAG,CAChB,OAAO,CAAC,GAAG,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;YAC3B,IAAI,SAAS,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;gBAAE,OAAO;YAEtC,MAAM,OAAO,GAAG,mBAAI,CAAC,IAAI,CAAC,UAAU,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YAClD,MAAM,QAAQ,GAAG,mBAAI,CAAC,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YAEpD,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;gBACzB,MAAM,kBAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC9C,MAAM,WAAW,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YACtC,CAAC;iBAAM,CAAC;gBACP,MAAM,kBAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;YACtC,CAAC;QACF,CAAC,CAAC,CACF,CAAC;IACH,CAAC;IAED,MAAM,kBAAE,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IACjD,MAAM,WAAW,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;AACxC,CAAC;AAEM,KAAK,UAAU,YAAY,CAAI,OAAmB,EAAE,KAAa;IACvE,MAAM,YAAY,GAAG,IAAI,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC;IAClE,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,CAAC;IAC5D,OAAO,MAAM,CAAC;AACf,CAAC;AAEM,KAAK,UAAU,aAAa,CAClC,QAAgB,EAChB,QAA6B;IAE7B,MAAM,kBAAE,CAAC,KAAK,CAAC,mBAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC5D,MAAM,kBAAE,CAAC,SAAS,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACxC,CAAC;AAEM,KAAK,UAAU,YAAY,CAAC,GAAW;IAC7C,OAAO,MAAM,kBAAE,CAAC,KAAK,CAAC,GAAG,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;AACjD,CAAC;AAEM,KAAK,UAAU,sBAAsB,CAC3C,OAAe,EACf,OAAe,EACf,OAAe;IAEf,MAAM,KAAK,GAAG,MAAM,kBAAE,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;IAExC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;QAC1B,MAAM,OAAO,GAAG,mBAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;QAC5C,MAAM,WAAW,GAAG,mBAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC3C,MAAM,WAAW,GAAG,WAAW;aAC7B,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC;aACzB,OAAO,CAAC,IAAA,uBAAS,EAAC,OAAO,CAAC,EAAE,IAAA,uBAAS,EAAC,OAAO,CAAC,CAAC,CAAC;QAElD,IAAI,WAAW,KAAK,WAAW,EAAE,CAAC;YACjC,MAAM,OAAO,GAAG,mBAAI,CAAC,OAAO,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;YACnD,MAAM,kBAAE,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACnC,CAAC;IACF,CAAC;AACF,CAAC;AAEM,KAAK,UAAU,eAAe,CAAC,UAAkB,EAAE,UAAkB;IAC3E,MAAM,SAAS,GAAG,mBAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;IAC3C,MAAM,UAAU,GAAG,mBAAI,CAAC,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;IACvD,MAAM,kBAAE,CAAC,MAAM,CAAC,UAAU,EAAE,UAAU,CAAC,CAAC;IACxC,OAAO,UAAU,CAAC;AACnB,CAAC"}