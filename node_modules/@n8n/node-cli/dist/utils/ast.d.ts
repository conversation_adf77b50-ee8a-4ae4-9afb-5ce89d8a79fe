import { type ClassDeclaration, type ObjectLiteralExpression } from 'ts-morph';
export declare const loadSingleSourceFile: (path: string) => import("ts-morph").SourceFile;
export declare const updateStringProperty: ({ obj, key, value, }: {
    obj: ObjectLiteralExpression | ClassDeclaration;
    key: string;
    value: string;
}) => void;
export declare const getChildObjectLiteral: ({ obj, key, }: {
    obj: ObjectLiteralExpression;
    key: string;
}) => ObjectLiteralExpression;
