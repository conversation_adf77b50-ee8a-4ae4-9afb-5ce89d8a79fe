{"version": 3, "file": "eslint.js", "sourceRoot": "", "sources": ["../../src/configs/eslint.ts"], "names": [], "mappings": ";;;;;;AAEA,oDAAgC;AAChC,0CAA8C;AAC9C,yFAAmF;AACnF,oFAAkD;AAClD,gGAA0D;AAC1D,0EAA+D;AAElD,QAAA,MAAM,GAAgB,2BAAQ,CAAC,MAAM,CACjD,IAAA,sBAAa,EAAC,CAAC,MAAM,CAAC,CAAC,EACvB;IACC,KAAK,EAAE,CAAC,SAAS,CAAC;IAClB,OAAO,EAAE;QACR,YAAM,CAAC,OAAO,CAAC,WAAW;QAC1B,2BAAQ,CAAC,OAAO,CAAC,WAAW;QAC5B,gCAAY,CAAC,OAAO,CAAC,kBAAkB,CAAC;KACxC;IACD,KAAK,EAAE;QACN,eAAe,EAAE,KAAK;KACtB;CACD,EACD;IACC,OAAO,EAAE,EAAE,gBAAgB,EAAE,sCAAc,EAAE;IAC7C,QAAQ,EAAE;QACT,wBAAwB,EAAE,CAAC,IAAA,kEAA8B,GAAE,CAAC;KAC5D;CACD,EACD;IACC,KAAK,EAAE,CAAC,cAAc,CAAC;IACvB,KAAK,EAAE;QACN,GAAG,sCAAc,CAAC,OAAO,CAAC,SAAS,CAAC,KAAK;KACzC;IACD,eAAe,EAAE;QAChB,MAAM,EAAE,2BAAQ,CAAC,MAAM;QACvB,aAAa,EAAE;YACd,mBAAmB,EAAE,CAAC,OAAO,CAAC;SAC9B;KACD;CACD,EACD;IACC,KAAK,EAAE,CAAC,uBAAuB,CAAC;IAChC,KAAK,EAAE;QACN,GAAG,sCAAc,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK;QAC3C,4DAA4D,EAAE,KAAK;KACnE;CACD,EACD;IACC,KAAK,EAAE,CAAC,iBAAiB,CAAC;IAC1B,KAAK,EAAE;QACN,GAAG,sCAAc,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK;QACrC,iEAAiE,EAAE,KAAK;QACxE,qDAAqD,EAAE,KAAK;QAC5D,0DAA0D,EAAE,KAAK;KACjE;CACD,CACD,CAAC;AAEF,kBAAe,cAAM,CAAC"}