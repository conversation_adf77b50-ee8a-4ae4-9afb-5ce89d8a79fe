{"compilerOptions": {"strict": true, "module": "commonjs", "moduleResolution": "node", "target": "es2019", "lib": ["es2019", "es2020", "es2022.error"], "removeComments": true, "useUnknownInCatchVariables": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": true, "noImplicitReturns": true, "noUnusedLocals": true, "strictNullChecks": true, "preserveConstEnums": true, "esModuleInterop": true, "resolveJsonModule": true, "incremental": true, "declaration": true, "sourceMap": true, "skipLibCheck": true}, "include": ["src/**/*.ts", "test/**/*.ts"], "exclude": ["**/dist/**/*", "**/node_modules/**/*"]}