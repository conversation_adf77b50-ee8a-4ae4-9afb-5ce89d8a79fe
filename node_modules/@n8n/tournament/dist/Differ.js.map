{"version": 3, "file": "Differ.js", "sourceRoot": "", "sources": ["../src/Differ.ts"], "names": [], "mappings": ";;;AACA,mCAAsC;AACtC,qCAAgD;AAEhD,MAAM,SAAS,GAAG,CAAC,IAA2B,EAAW,EAAE;;IAC1D,OAAO,CAAA,MAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,0CAAE,IAAI,MAAK,cAAc,CAAC;AACtD,CAAC,CAAC;AACF,MAAM,UAAU,GAAG,CAAC,IAA2B,EAAE,EAAE;;IAClD,OAAO,MAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAmC,0CAAE,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC/E,CAAC,CAAC;AAEF,MAAM,qBAAqB,GAAG,CAAC,IAA2B,EAAW,EAAE;;IACtE,OAAO,CACN,CAAA,MAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,0CAAE,IAAI,MAAK,iBAAiB;QAChD,CAAA,MAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,0CAAE,IAAI,MAAK,gBAAgB;QACxD,CAAA,MAAA,MAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,0CAAE,SAAS,CAAC,CAAC,CAAC,0CAAE,IAAI,MAAK,SAAS;QAC/D,CAAA,MAAA,MAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,0CAAE,SAAS,CAAC,CAAC,CAAC,0CAAE,KAAK,MAAK,EAAE;QACzD,CAAA,MAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,0CAAE,MAAM,CAAC,IAAI,MAAK,kBAAkB;QACjE,CAAA,MAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,0CAAE,MAAM,CAAC,MAAM,CAAC,IAAI,MAAK,iBAAiB;QACvE,CAAA,MAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,0CAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,MAAK,YAAY;QACpE,CAAA,MAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,0CAAE,MAAM,CAAC,QAAQ,CAAC,IAAI,MAAK,MAAM,CAC9D,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,sBAAsB,GAAG,CAAC,IAA2B,EAAmB,EAAE;;IAC/E,IACC,CAAC,CACA,CAAA,MAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,0CAAE,IAAI,MAAK,iBAAiB;QAChD,CAAA,MAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,0CAAE,IAAI,MAAK,gBAAgB;QACxD,CAAA,MAAA,MAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,0CAAE,SAAS,CAAC,CAAC,CAAC,0CAAE,IAAI,MAAK,SAAS;QAC/D,CAAA,MAAA,MAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,0CAAE,SAAS,CAAC,CAAC,CAAC,0CAAE,KAAK,MAAK,EAAE;QACzD,CAAA,MAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,0CAAE,MAAM,CAAC,IAAI,MAAK,kBAAkB;QACjE,CAAA,MAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,0CAAE,MAAM,CAAC,MAAM,CAAC,IAAI,MAAK,iBAAiB,CACvE,EACA;QACD,OAAO,EAAE,CAAC;KACV;IACD,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ;SACzD,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;QACV,IACC,CAAA,CAAC,aAAD,CAAC,uBAAD,CAAC,CAAE,IAAI,MAAK,gBAAgB;YAC5B,CAAC,CAAC,MAAM,CAAC,IAAI,KAAK,kBAAkB;YACpC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,oBAAoB,EAC5C;YACD,OAAO,IAAI,CAAC;SACZ;QACD,MAAM,SAAS,GAAG,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QAC/C,IAAI,SAAS,CAAC,IAAI,KAAK,cAAc,EAAE;YACtC,OAAO,SAAS,CAAC;SACjB;QACD,OAAO,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAChC,CAAC,CAAC;SACD,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,IAAI,CAAoB,CAAC;AAChD,CAAC,CAAC;AAEK,MAAM,WAAW,GAAG,CAAC,IAAY,EAAE,KAAa,EAAW,EAAE;IACnE,MAAM,UAAU,GAAG,IAAA,cAAK,EAAC,IAAI,EAAE;QAC9B,MAAM,EAAE,EAAE,KAAK,EAAE,6BAAoB,EAAE;KACvC,CAA0B,CAAC;IAC5B,MAAM,WAAW,GAAG,IAAA,cAAK,EAAC,KAAK,EAAE;QAChC,MAAM,EAAE,EAAE,KAAK,EAAE,6BAAoB,EAAE;KACvC,CAA0B,CAAC;IAE5B,MAAM,YAAY,GAAU,EAAE,CAAC;IAC/B,IAAI,IAAI,GAAG,cAAK,CAAC,qBAAqB,CAAC,UAAU,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC;IAQ9E,IAAI,CAAC,IAAI,EAAE;QACV,IAAI,SAAS,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE;YAErD,MAAM,YAAY,GAAG,UAAU,CAAC,WAAW,CAAC,CAAC;YAC7C,IAAI,GAAG,cAAK,CAAC,qBAAqB,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;SAC7E;aAAM,IAAI,qBAAqB,CAAC,WAAW,CAAC,IAAI,qBAAqB,CAAC,UAAU,CAAC,EAAE;YAEnF,MAAM,UAAU,GAAG,sBAAsB,CAAC,WAAW,CAAC,CAAC;YACvD,MAAM,SAAS,GAAG,sBAAsB,CAAC,UAAU,CAAC,CAAC;YACrD,IAAI,UAAU,CAAC,MAAM,KAAK,SAAS,CAAC,MAAM,EAAE;gBAC3C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC3C,IAAI,GAAG,cAAK,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;oBAC9E,IAAI,CAAC,IAAI,EAAE;wBACV,MAAM;qBACN;iBACD;aACD;SACD;KACD;IAED,OAAO,CAAC,IAAI,CAAC;AACd,CAAC,CAAC;AAtCW,QAAA,WAAW,eAsCtB"}