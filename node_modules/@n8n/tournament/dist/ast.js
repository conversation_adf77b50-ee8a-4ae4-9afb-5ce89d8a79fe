"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.astBuilders = exports.astVisit = void 0;
var recast_1 = require("recast");
Object.defineProperty(exports, "astVisit", { enumerable: true, get: function () { return recast_1.visit; } });
var ast_types_1 = require("ast-types");
Object.defineProperty(exports, "astBuilders", { enumerable: true, get: function () { return ast_types_1.builders; } });
//# sourceMappingURL=ast.js.map