{"version": 3, "file": "VariablePolyfill.js", "sourceRoot": "", "sources": ["../src/VariablePolyfill.ts"], "names": [], "mappings": ";;;AACA,mCAA+B;AAI/B,yCAA0C;AAE1C,2CAAqD;AAErD,SAAS,WAAW,CAAC,KAAY;IAChC,OAAO,IAAI,CAAC;AACb,CAAC;AAEY,QAAA,gBAAgB,GAAG,oBAAC,CAAC,UAAU,CAG3C,OAAO,MAAM,KAAK,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAChD,CAAC;AAEF,MAAM,iBAAiB,GAAG,CAAC,IAAiC,EAAE,QAAkB,EAAE,EAAE;IACnF,OAAO,oBAAC,CAAC,gBAAgB,CACxB,oBAAC,CAAC,qBAAqB,CACtB,oBAAC,CAAC,gBAAgB,CAAC,IAAI,EAAE,oBAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,CAAC,EACxD,QAAQ,EACR,wBAAgB,CAChB,EACD,oBAAC,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CACvB,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,SAAS,GAAG,CAAC,IAA2C,EAAE,EAAE;IACjE,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC;IACvB,OAAO,KAAK,KAAK,IAAI,EAAE;QACtB,IAAI,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;YACnC,OAAO,IAAI,CAAC;SACZ;QACD,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC;KACrB;IACD,OAAO,KAAK,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,kBAAkB,GAAG,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAExD,MAAM,WAAW,GAAG,CACnB,IAA2C,EAC3C,QAAkB,EAClB,QAAiB,KAAK,EACrB,EAAE;IACH,IAAI,CAAC,KAAK,EAAE;QACX,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE;YAEpB,OAAO;SACP;KACD;IAED,IAAI,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;QAChD,OAAO;KACP;IACD,IAAI,CAAC,OAAO,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC;AACtD,CAAC,CAAC;AAUF,MAAM,aAAa,GAAuD;IACzE,gBAAgB,CAAC,IAAI,EAAE,MAAmC,EAAE,QAAQ;QACnE,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE;YACnD,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SAC5B;IACF,CAAC;IACD,wBAAwB,CAAC,IAAI,EAAE,MAA2C,EAAE,QAAQ;QACnF,IAAI,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE;YAChC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SAC5B;IACF,CAAC;IACD,QAAQ,CAAC,IAAI,EAAE,MAA2B,EAAE,QAAQ;;QACnD,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,KAAK,EAAE;YAC/B,OAAO;SACP;QACD,MAAM,UAAU,GAA6B,MAAA,MAAA,IAAI,CAAC,MAAM,0CAAE,MAAM,0CAAE,IAAI,CAAC;QACvE,IAAI,CAAC,UAAU,EAAE;YAChB,OAAO;SACP;QACD,MAAM,SAAS,GAA2B,MAAA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,0CAAE,IAAI,CAAC;QAC1E,IAAI,CAAC,SAAS,EAAE;YACf,OAAO;SACP;QACD,IAAI,SAAS,CAAC,IAAI,KAAK,oBAAoB,IAAI,SAAS,CAAC,EAAE,KAAK,UAAU,EAAE;YAC3E,OAAO;SACP;QAED,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC;QACzB,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;IAC7B,CAAC;IACD,iBAAiB,CAAC,IAAI,EAAE,MAAoC,EAAE,QAAQ;QACrE,IAAI,MAAM,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE;YAC/B,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SAC5B;IACF,CAAC;IACD,kBAAkB,CAAC,IAAI,EAAE,MAAqC,EAAE,QAAQ;QACvE,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,EAAE;YAC9B,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;SAC5B;IACF,CAAC;CACD,CAAC;AAEK,MAAM,kBAAkB,GAAG,CACjC,GAA0B,EAC1B,QAAkB,EACY,EAAE;IAChC,IAAA,cAAK,EAAC,GAAG,EAAE;QACV,qBAAqB,CAAC,KAAK;YAC1B,MAAM,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC;QAC9C,CAAC;QACD,eAAe,CAAC,IAAI;YACnB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACpB,MAAM,MAAM,GAAe,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YAG5C,IAAI,kCAAsB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACpD,OAAO;aACP;YAED,QAAQ,MAAM,CAAC,IAAI,EAAE;gBACpB,KAAK,mBAAmB,CAAC;gBACzB,KAAK,UAAU,CAAC;gBAChB,KAAK,kBAAkB,CAAC;gBACxB,KAAK,0BAA0B,CAAC;gBAChC,KAAK,oBAAoB;oBACxB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;wBAChC,MAAM,IAAI,KAAK,CAAC,kDAAkD,MAAM,CAAC,IAAI,EAAE,CAAC,CAAC;qBACjF;oBACD,aAAa,CAAC,MAAM,CAAC,IAAI,CAAE,CAAC,IAAI,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC;oBACpD,MAAM;gBACP,KAAK,kBAAkB,CAAC;gBACxB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,sBAAsB,CAAC;gBAC5B,KAAK,oBAAoB,CAAC;gBAC1B,KAAK,iBAAiB,CAAC;gBACvB,KAAK,kBAAkB,CAAC;gBACxB,KAAK,mBAAmB,CAAC;gBACzB,KAAK,uBAAuB,CAAC;gBAC7B,KAAK,eAAe,CAAC;gBACrB,KAAK,gBAAgB,CAAC;gBACtB,KAAK,wBAAwB,CAAC;gBAC9B,KAAK,0BAA0B,CAAC;gBAChC,KAAK,iBAAiB,CAAC;gBACvB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,kBAAkB,CAAC;gBACxB,KAAK,cAAc,CAAC;gBACpB,KAAK,aAAa,CAAC;gBACnB,KAAK,gBAAgB,CAAC;gBACtB,KAAK,gBAAgB,CAAC;gBACtB,KAAK,gBAAgB,CAAC;gBACtB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,kBAAkB,CAAC;gBACxB,KAAK,qBAAqB,CAAC;gBAC3B,KAAK,mBAAmB,CAAC;gBACzB,KAAK,gBAAgB,CAAC;gBACtB,KAAK,eAAe,CAAC;gBACrB,KAAK,iBAAiB;oBACrB,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAC5B,MAAM;gBAGP,KAAK,OAAO,CAAC;gBACb,KAAK,YAAY,CAAC;gBAClB,KAAK,yBAAyB,CAAC;gBAC/B,KAAK,qBAAqB,CAAC;gBAC3B,KAAK,oBAAoB,CAAC;gBAC1B,KAAK,gBAAgB,CAAC;gBACtB,KAAK,kBAAkB,CAAC;gBACxB,KAAK,cAAc,CAAC;gBACpB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,aAAa,CAAC;gBACnB,KAAK,yBAAyB,CAAC;gBAC/B,KAAK,QAAQ,CAAC;gBACd,KAAK,qBAAqB,CAAC;gBAC3B,KAAK,aAAa,CAAC;gBACnB,KAAK,gBAAgB,CAAC;gBACtB,KAAK,cAAc,CAAC;gBACpB,KAAK,gBAAgB,CAAC;gBACtB,KAAK,kBAAkB,CAAC;gBACxB,KAAK,gBAAgB,CAAC;gBACtB,KAAK,mBAAmB,CAAC;gBACzB,KAAK,mBAAmB,CAAC;gBACzB,KAAK,mBAAmB,CAAC;gBACzB,KAAK,mBAAmB,CAAC;gBACzB,KAAK,sBAAsB,CAAC;gBAC5B,KAAK,0BAA0B,CAAC;gBAChC,KAAK,MAAM,CAAC;gBACZ,KAAK,aAAa,CAAC;gBACnB,KAAK,oBAAoB,CAAC;gBAC1B,KAAK,aAAa,CAAC;gBACnB,KAAK,cAAc,CAAC;gBACpB,KAAK,eAAe,CAAC;gBACrB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,kBAAkB,CAAC;gBACxB,KAAK,uBAAuB,CAAC;gBAC7B,KAAK,gBAAgB,CAAC;gBACtB,KAAK,kBAAkB,CAAC;gBACxB,KAAK,kBAAkB,CAAC;gBACxB,KAAK,eAAe,CAAC;gBACrB,KAAK,aAAa,CAAC;gBACnB,KAAK,WAAW,CAAC;gBACjB,KAAK,wBAAwB,CAAC;gBAC9B,KAAK,sBAAsB,CAAC;gBAC5B,KAAK,uBAAuB,CAAC;gBAC7B,KAAK,iBAAiB;oBACrB,MAAM;gBAGP,KAAK,sBAAsB,CAAC;gBAC5B,KAAK,uBAAuB,CAAC;gBAC7B,KAAK,yBAAyB;oBAC7B,MAAM;gBAGP,KAAK,cAAc,CAAC;gBACpB,KAAK,eAAe,CAAC;gBACrB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,kBAAkB,CAAC;gBACxB,KAAK,kBAAkB,CAAC;gBACxB,KAAK,mBAAmB,CAAC;gBACzB,KAAK,sBAAsB,CAAC;gBAC5B,KAAK,0BAA0B,CAAC;gBAChC,KAAK,6BAA6B,CAAC;gBACnC,KAAK,sBAAsB,CAAC;gBAC5B,KAAK,WAAW,CAAC;gBACjB,KAAK,YAAY,CAAC;gBAClB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,oBAAoB;oBACxB,MAAM;gBAGP,KAAK,gBAAgB,CAAC;gBACtB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,iBAAiB,CAAC;gBACvB,KAAK,kBAAkB,CAAC;gBACxB,KAAK,mBAAmB,CAAC;gBACzB,KAAK,mBAAmB,CAAC;gBACzB,KAAK,mBAAmB,CAAC;gBACzB,KAAK,oBAAoB,CAAC;gBAC1B,KAAK,qBAAqB,CAAC;gBAC3B,KAAK,qBAAqB,CAAC;gBAC3B,KAAK,qBAAqB,CAAC;gBAC3B,KAAK,qBAAqB,CAAC;gBAC3B,KAAK,sBAAsB,CAAC;gBAC5B,KAAK,uBAAuB,CAAC;gBAC7B,KAAK,wBAAwB,CAAC;gBAC9B,KAAK,wBAAwB,CAAC;gBAC9B,KAAK,2BAA2B,CAAC;gBACjC,KAAK,2BAA2B,CAAC;gBACjC,KAAK,2BAA2B,CAAC;gBACjC,KAAK,4BAA4B,CAAC;gBAClC,KAAK,4BAA4B,CAAC;gBAClC,KAAK,8BAA8B,CAAC;gBACpC,KAAK,iCAAiC;oBACrC,MAAM;gBAGP,KAAK,kBAAkB,CAAC;gBACxB,KAAK,eAAe,CAAC;gBACrB,KAAK,gBAAgB,CAAC;gBACtB,KAAK,eAAe,CAAC;gBACrB,KAAK,aAAa,CAAC;gBACnB,KAAK,SAAS,CAAC;gBACf,KAAK,eAAe,CAAC;gBACrB,KAAK,gBAAgB,CAAC;gBACtB,KAAK,gBAAgB;oBACpB,MAAM;gBAGP,KAAK,cAAc,CAAC;gBACpB,KAAK,gBAAgB;oBACpB,MAAM;gBAGP,KAAK,eAAe,CAAC;gBACrB,KAAK,SAAS,CAAC;gBACf,KAAK,YAAY,CAAC;gBAClB,KAAK,aAAa,CAAC;gBACnB,KAAK,qBAAqB,CAAC;gBAC3B,KAAK,wBAAwB;oBAC5B,MAAM;gBAIP,KAAK,yBAAyB,CAAC;gBAC/B,KAAK,qBAAqB;oBACzB,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;oBAC5B,MAAM;gBAEP;oBAGC,WAAW,CAAC,MAAM,CAAC,CAAC;oBACpB,MAAM;aACP;QACF,CAAC;KACD,CAAC,CAAC;IAEH,OAAO,GAAG,CAAC,OAAO,CAAC,IAAuB,CAAC;AAC5C,CAAC,CAAC;AAzMW,QAAA,kBAAkB,sBAyM7B"}