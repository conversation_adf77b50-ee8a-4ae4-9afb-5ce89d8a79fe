{"version": 3, "file": "Parser.js", "sourceRoot": "", "sources": ["../src/Parser.ts"], "names": [], "mappings": ";;;AAAA,+CAAqD;AAErD,0CAA4C;AAE5C,SAAgB,oBAAoB,CAAC,MAAc,EAAE,OAAa;IACjE,IAAI;QACH,MAAM,GAAG,GAAG,IAAA,oBAAY,EAAC,MAAM,EAAE;YAChC,GAAG,EAAE,IAAI;YACT,SAAS,EAAE,IAAI;YACf,OAAO,EAAE,IAAI;YACb,KAAK,EAAE,IAAA,gBAAS,EAAC,OAAO,EAAE,OAAO,EAAE,KAAK,CAAY;YACpD,QAAQ,EAAE,IAAA,gBAAS,EAAC,OAAO,EAAE,UAAU,EAAE,IAAI,CAAY;YACzD,MAAM,EAAE,IAAI;YACZ,GAAG,EAAE,IAAA,gBAAS,EAAC,OAAO,EAAE,KAAK,EAAE,KAAK,CAAY;YAChD,UAAU,EAAE,IAAA,gBAAS,EAAC,OAAO,EAAE,YAAY,EAAE,QAAQ,CAAW;SAC/C,CAAC,CAAC;QAEpB,OAAO,GAAG,CAAC;KACX;IAAC,OAAO,KAAK,EAAE;QACf,IAAI,KAAK,YAAY,KAAK;YAAE,MAAM,IAAI,WAAW,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACjE,MAAM,KAAK,CAAC;KACZ;AACF,CAAC;AAlBD,oDAkBC"}