{"version": 3, "file": "ExpressionBuilder.js", "sourceRoot": "", "sources": ["../src/ExpressionBuilder.ts"], "names": [], "mappings": ";;;AAEA,mCAA6C;AAE7C,yCAA0C;AAG1C,yDAA0E;AAE1E,6DAAuD;AAEvD,qCAAgD;AAUhD,MAAM,CAAC,GAAG,oBAAC,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;AAE5B,MAAM,oBAAoB,GAAG,CAAC,QAAQ,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;AAE1D,MAAM,eAAe,GAAG,CAAC,IAAwB,EAAE,EAAE;IACpD,IAAI,UAAU,GAAG,KAAK,CAAC;IAEvB,IAAA,cAAK,EAAC,IAAI,EAAE;QACX,qBAAqB;YACpB,UAAU,GAAG,IAAI,CAAC;YAClB,OAAO,KAAK,CAAC;QACd,CAAC;QACD,mBAAmB;YAClB,UAAU,GAAG,IAAI,CAAC;YAClB,OAAO,KAAK,CAAC;QACd,CAAC;QACD,eAAe,CAAC,IAAI;YACnB,IAAI,oBAAoB,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBAClD,UAAU,GAAG,IAAI,CAAC;gBAClB,OAAO,KAAK,CAAC;aACb;YACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACpB,OAAO;QACR,CAAC;KACD,CAAC,CAAC;IAEH,OAAO,UAAU,CAAC;AACnB,CAAC,CAAC;AAEF,MAAM,WAAW,GAAG,CAAC,IAA8B,EAAE,EAAE;IACtD,IAAI,KAAK,GAAG,KAAK,CAAC;IAElB,IAAA,cAAK,EAAC,IAAI,EAAE;QACX,uBAAuB;YACtB,KAAK,GAAG,IAAI,CAAC;YACb,OAAO,KAAK,CAAC;QACd,CAAC;QACD,wBAAwB;YACvB,KAAK,GAAG,IAAI,CAAC;YACb,OAAO,KAAK,CAAC;QACd,CAAC;QACD,4BAA4B;YAC3B,KAAK,GAAG,IAAI,CAAC;YACb,OAAO,KAAK,CAAC;QACd,CAAC;KACD,CAAC,CAAC;IAEH,OAAO,KAAK,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,iBAAiB,GAAG,CAAC,IAA8B,EAAE,EAAE;IAC5D,IAAI,OAAO,GAAG,KAAK,CAAC;IAEpB,IAAA,cAAK,EAAC,IAAI,EAAE;QACX,oBAAoB,CAAC,IAAI;YACxB,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;gBACjC,OAAO,GAAG,IAAI,CAAC;gBACf,OAAO,KAAK,CAAC;aACb;YACD,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACpB,OAAO;QACR,CAAC;KACD,CAAC,CAAC;IAEH,OAAO,OAAO,CAAC;AAChB,CAAC,CAAC;AAEF,MAAM,kBAAkB,GAAG,CAAC,IAAmB,EAAE,EAAE;IAClD,OAAO,oBAAC,CAAC,YAAY,CACpB,oBAAC,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,CAAC,EACxB,oBAAC,CAAC,WAAW,CACZ,oBAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EACjB,IAAI,EACJ,oBAAC,CAAC,cAAc,CAAC;QAChB,oBAAC,CAAC,mBAAmB,CACpB,oBAAC,CAAC,cAAc,CAAC,oBAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,CAAC,oBAAC,CAAC,UAAU,CAAC,GAAG,CAAC,EAAE,oBAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAC5E;KACD,CAAC,CACF,CACD,CAAC;AACH,CAAC,CAAC;AAEF,MAAM,aAAa,GAAG,CAAC,IAAY,EAAU,EAAE;IAC9C,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;QAChC,OAAO,GAAG,GAAG,IAAI,GAAG,GAAG,CAAC;KACxB;IACD,OAAO,IAAI,CAAC;AACb,CAAC,CAAC;AAEF,MAAM,iBAAiB,GAAG,CAAC,IAAoB,EAAE,EAAE;IAClD,OAAO,oBAAC,CAAC,cAAc,CAAC;QAEvB,oBAAC,CAAC,mBAAmB,CAAC,oBAAC,CAAC,oBAAoB,CAAC,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAK3D,oBAAC,CAAC,eAAe,CAChB,oBAAC,CAAC,qBAAqB,CACtB,oBAAC,CAAC,iBAAiB,CAClB,IAAI,EACJ,oBAAC,CAAC,iBAAiB,CAAC,IAAI,EAAE,CAAC,EAAE,oBAAC,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC,EAAE,oBAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,EACxE,oBAAC,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC,EAAE,oBAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAC9C,EACD,CAAC,EACD,oBAAC,CAAC,OAAO,CAAC,EAAE,CAAC,CACb,CACD;KACD,CAAC,CAAC;AACJ,CAAC,CAAC;AAMF,MAAM,iBAAiB,GAAG,CAAC,IAA2B,EAAyB,EAAE;IAChF,MAAM,OAAO,GAAG,CAAC,GAAW,EAAU,EAAE;QACvC,OAAO,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAClC,CAAC,CAAC;IACF,IAAA,cAAK,EAAC,IAAI,EAAE;QACX,oBAAoB,CAAC,IAAI;YACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YACpB,MAAM,EAAE,GAAG,oBAAC,CAAC,eAAe,CAC3B;gBACC,MAAM,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;gBAChF,GAAG,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;aACjC,EACD,IAAI,CAAC,IAAI,CAAC,IAAI,CACd,CAAC;YACF,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;QAClB,CAAC;KACD,CAAC,CAAC;IAEH,OAAO,IAAI,CAAC;AACb,CAAC,CAAC;AAEK,MAAM,mBAAmB,GAAG,CAAC,IAAY,EAAsC,EAAE;IACvF,OAAO,IAAA,oCAAe,EAAC,IAAI,CAAC,CAAC,GAAG,CAA8B,CAAC,KAAK,EAAE,EAAE;QACvE,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE;YAC1B,MAAM,IAAI,GAAG,aAAa,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACvC,MAAM,IAAI,GAAG,IAAA,cAAK,EAAC,IAAI,EAAE;gBACxB,MAAM,EAAE,EAAE,KAAK,EAAE,6BAAoB,EAAE;aACvC,CAA0B,CAAC;YAE5B,OAAO,EAAE,GAAG,KAAK,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC;SAClC;QACD,OAAO,KAAK,CAAC;IACd,CAAC,CAAC,CAAC;AACJ,CAAC,CAAC;AAZW,QAAA,mBAAmB,uBAY9B;AAEK,MAAM,iBAAiB,GAAG,CAChC,IAAY,EACZ,YAAoB,EACpB,KAAsB,EACS,EAAE;;IACjC,MAAM,MAAM,GAAG,IAAA,2BAAmB,EAAC,IAAI,CAAC,CAAC;IAEzC,MAAM,OAAO,GAAG,oBAAC,CAAC,OAAO,CAAC;QACzB,oBAAC,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC,oBAAC,CAAC,kBAAkB,CAAC,mCAAgB,EAAE,oBAAC,CAAC,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;KAC9F,CAAC,CAAC;IAOH,IAAI,QAAQ,GAAa,oBAAC,CAAC,cAAc,EAAE,CAAC;IAC5C,MAAM,KAAK,GAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAClF,WAAW,CAAC,CAAC,CAAC,MAAM,CAAC,CACrB,CAAC;IACF,IAAI,KAAK,EAAE;QACV,QAAQ,GAAG,oBAAC,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QACtC,OAAO,CAAC,IAAI,CAAC,IAAI,CAChB,oBAAC,CAAC,mBAAmB,CAAC,KAAK,EAAE,CAAC,oBAAC,CAAC,kBAAkB,CAAC,QAAQ,EAAE,oBAAC,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,CAClF,CAAC;KACF;IAED,MAAM,aAAa,GAAI,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAkB,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAC1F,iBAAiB,CAAC,CAAC,CAAC,MAAM,CAAC,CAC3B,CAAC;IAMF,IACC,MAAM,CAAC,MAAM,GAAG,CAAC;QACjB,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE;QAErB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,EAAE,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,EAC7C;QACD,IAAI,KAAK,GAAqB,EAAE,CAAC;QACjC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAE3B,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE;gBAC1B,KAAK,CAAC,IAAI,CAAC,oBAAC,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;aAElC;iBAAM;gBACN,MAAM,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBAC9C,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE;oBAChC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;iBACtB;gBACD,MAAM,MAAM,GAAG,MAAA,IAAA,qCAAkB,EAAC,KAAK,EAAE,QAAQ,CAAC,0CAAG,CAAC,CAAC,CAAC;gBACxD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,qBAAqB,EAAE;oBACrD,MAAM,IAAI,WAAW,CAAC,4BAA4B,CAAC,CAAC;iBACpD;gBAED,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE;oBAC/B,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;iBACvB;gBAED,MAAM,YAAY,GAAG,iBAAiB,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;gBAE1D,IAAI,eAAe,CAAC,MAAM,CAAC,EAAE;oBAG5B,YAAY,CAAC,IAAI,GAAG;wBACnB,kBAAkB,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;wBAKxC,oBAAC,CAAC,mBAAmB,CAAC,oBAAC,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC;wBACvC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC;qBACpB,CAAC;iBACF;gBAKD,KAAK,CAAC,IAAI,CACT,oBAAC,CAAC,cAAc,CACf,oBAAC,CAAC,gBAAgB,CAAC,oBAAC,CAAC,kBAAkB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,oBAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EACvF,CAAC,oBAAC,CAAC,cAAc,EAAE,CAAC,CACpB,CACD,CAAC;aACF;SACD;QAGD,IAAI,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YACtB,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,oBAAC,CAAC,eAAe,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAC/C;aAAM;YAEN,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,SAAS,IAAI,CAAC,CAAC,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC;YACvE,OAAO,CAAC,IAAI,CAAC,IAAI,CAChB,oBAAC,CAAC,eAAe,CAChB,oBAAC,CAAC,cAAc,CAAC,oBAAC,CAAC,gBAAgB,CAAC,oBAAC,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE,oBAAC,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE;gBACpF,oBAAC,CAAC,OAAO,CAAC,EAAE,CAAC;aACb,CAAC,CACF,CACD,CAAC;SACF;KACD;SAAM;QACN,MAAM,KAAK,GAAG,iBAAiB,CAAE,MAAM,CAAC,CAAC,CAAgB,CAAC,MAAM,CAAC,CAAC;QAClE,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,MAAM,EAAE;YAChC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAC;SACtB;QACD,MAAM,MAAM,GAAG,MAAA,IAAA,qCAAkB,EAAC,KAAK,EAAE,QAAQ,CAAC,0CAAG,CAAC,CAAC,CAAC;QACxD,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,IAAI,KAAK,qBAAqB,EAAE;YACrD,MAAM,IAAI,WAAW,CAAC,4BAA4B,CAAC,CAAC;SACpD;QAED,KAAK,MAAM,IAAI,IAAI,KAAK,CAAC,KAAK,EAAE;YAC/B,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;SACvB;QAED,IAAI,OAAO,GAAkB,oBAAC,CAAC,eAAe,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;QAClE,IAAI,eAAe,CAAC,MAAM,CAAC,EAAE;YAC5B,OAAO,GAAG,kBAAkB,CAAC,OAAO,CAAC,CAAC;SACtC;QACD,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;KAC3B;IAED,OAAO,CAAC,IAAA,cAAK,EAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,cAAc,EAAE,aAAa,EAAE,EAAE,CAAC,CAAC;AAC3F,CAAC,CAAC;AA7HW,QAAA,iBAAiB,qBA6H5B"}