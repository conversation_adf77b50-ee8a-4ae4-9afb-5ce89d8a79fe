{"version": 3, "file": "ExpressionSplitter.js", "sourceRoot": "", "sources": ["../src/ExpressionSplitter.ts"], "names": [], "mappings": ";;;AAeA,MAAM,YAAY,GAAG,mBAAmB,CAAC;AACzC,MAAM,aAAa,GAAG,mBAAmB,CAAC;AAEnC,MAAM,UAAU,GAAG,CAAC,IAAY,EAAU,EAAE;IAClD,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;AACnC,CAAC,CAAC;AAFW,QAAA,UAAU,cAErB;AAEF,MAAM,oBAAoB,GAAG,CAAC,IAAY,EAAU,EAAE;IACrD,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;AACpC,CAAC,CAAC;AAEK,MAAM,eAAe,GAAG,CAAC,UAAkB,EAAqB,EAAE;;IACxE,MAAM,MAAM,GAAsB,EAAE,CAAC;IACrC,IAAI,YAAY,GAAqB,MAAM,CAAC;IAC5C,IAAI,WAAW,GAAG,YAAY,CAAC;IAE/B,IAAI,MAAM,GAAG,EAAE,CAAC;IAEhB,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,OAAO,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE;QACjC,MAAM,IAAI,GAAG,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACrC,MAAM,GAAG,GAAG,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAInC,IAAI,CAAC,CAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,MAAM,CAAA,EAAE;YACjB,MAAM,IAAI,IAAI,CAAC;YACf,IAAI,YAAY,KAAK,MAAM,EAAE;gBAC5B,MAAM,CAAC,IAAI,CAAC;oBACX,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,MAAM;iBACZ,CAAC,CAAC;aACH;iBAAM;gBACN,MAAM,CAAC,IAAI,CAAC;oBACX,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,IAAA,kBAAU,EAAC,MAAM,CAAC;oBACxB,kBAAkB,EAAE,KAAK;iBACzB,CAAC,CAAC;aACH;YACD,MAAM;SACN;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;QAC7C,MAAM,cAAc,GAAG,MAAA,MAAA,MAAA,WAAW,CAAC,KAAK,CAAC,MAAM,CAAC,0CAAG,CAAC,CAAC,0CAAE,MAAM,mCAAI,CAAC,CAAC;QACnE,MAAM,SAAS,GAAG,cAAc,GAAG,CAAC,KAAK,CAAC,CAAC;QAE3C,IAAI,SAAS,EAAE;YACd,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC;YACjD,KAAK,IAAI,GAAG,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC;SACjC;aAAM;YACN,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;YAEnC,IAAI,YAAY,KAAK,MAAM,EAAE;gBAC5B,MAAM,CAAC,IAAI,CAAC;oBACX,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,oBAAoB,CAAC,MAAM,CAAC;iBAClC,CAAC,CAAC;gBACH,YAAY,GAAG,OAAO,CAAC;gBACvB,WAAW,GAAG,aAAa,CAAC;aAC5B;iBAAM;gBACN,MAAM,CAAC,IAAI,CAAC;oBACX,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,IAAA,kBAAU,EAAC,MAAM,CAAC;oBACxB,kBAAkB,EAAE,IAAI;iBACxB,CAAC,CAAC;gBACH,YAAY,GAAG,MAAM,CAAC;gBACtB,WAAW,GAAG,YAAY,CAAC;aAC3B;YAED,KAAK,IAAI,GAAG,CAAC,KAAK,GAAG,CAAC,CAAC;YACvB,MAAM,GAAG,EAAE,CAAC;SACZ;KACD;IAED,OAAO,MAAM,CAAC;AACf,CAAC,CAAC;AAjEW,QAAA,eAAe,mBAiE1B;AAGF,MAAM,oBAAoB,GAAG,CAAC,IAAY,EAAE,EAAE;IAC7C,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;AACnC,CAAC,CAAC;AAEK,MAAM,cAAc,GAAG,CAAC,KAAwB,EAAU,EAAE;IAClE,OAAO,KAAK;SACV,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;QACd,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE;YAC1B,OAAO,KAAK,oBAAoB,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC,kBAAkB,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;SACtF;QACD,OAAO,KAAK,CAAC,IAAI,CAAC;IACnB,CAAC,CAAC;SACD,IAAI,CAAC,EAAE,CAAC,CAAC;AACZ,CAAC,CAAC;AATW,QAAA,cAAc,kBASzB"}