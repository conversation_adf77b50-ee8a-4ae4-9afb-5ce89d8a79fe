{"version": 3, "file": "Analysis.js", "sourceRoot": "", "sources": ["../src/Analysis.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,wDAA0C;AAC1C,mCAAsC;AACtC,qCAAuC;AAGvC,2DAA6E;AAC7E,6DAAsD;AAkB/C,MAAM,iBAAiB,GAAG,CAAC,IAAY,EAAE,YAAoB,EAAkB,EAAE;IACvF,IAAI,CAAC,IAAI,EAAE;QACV,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;KACtB;IACD,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,KAAK,OAAO,EAAE;QAChD,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC;KAC3B;IACD,IAAI,WAA0B,CAAC;IAC/B,IAAI,UAAyB,CAAC;IAC9B,IAAI,QAAmC,CAAC;IACxC,IAAI;QACH,CAAC,WAAW,EAAE,QAAQ,CAAC,GAAG,IAAA,qCAAiB,EAAC,IAAI,EAAE,YAAY,EAAE,EAAE,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,EAAE,EAAE,CAAC,CAAC;KAC3F;IAAC,OAAO,CAAC,EAAE;QACX,WAAW,GAAG,IAAI,CAAC;QACnB,QAAQ,GAAG,IAAI,CAAC;KAChB;IACD,IAAI;QACH,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KACpC;IAAC,OAAO,CAAC,EAAE;QACX,UAAU,GAAG,IAAI,CAAC;KAClB;IACD,IAAI,CAAA,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,GAAG,CAAC,QAAQ,MAAI,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,GAAG,CAAC,cAAc,CAAA,EAAE;QAC3D,OAAO;YACN,IAAI,EAAE,KAAK;YACX,UAAU,EAAE,IAAA,mCAA2B,EAAC,IAAI,CAAC;YAC7C,GAAG,EAAE,QAAQ,CAAC,GAAG;SACjB,CAAC;KACF;IACD,IAAI,WAAW,KAAK,IAAI,IAAI,UAAU,KAAK,IAAI,EAAE;QAEhD,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;KACtB;SAAM,IAAI,WAAW,KAAK,IAAI,EAAE;QAEhC,OAAO;YACN,IAAI,EAAE,KAAK;YACX,UAAU,EAAE,aAAa;YACzB,WAAW,EAAE;gBACZ,IAAI,EAAE,KAAK;gBACX,UAAU,EAAE,IAAI;aAChB;SACD,CAAC;KACF;SAAM,IAAI,UAAU,KAAK,IAAI,EAAE;QAE/B,OAAO;YACN,IAAI,EAAE,KAAK;YACX,UAAU,EAAE,IAAA,mCAA2B,EAAC,IAAI,CAAC;YAC7C,WAAW,EAAE;gBACZ,IAAI,EAAE,IAAI;gBACV,UAAU,EAAE,KAAK;aACjB;SACD,CAAC;KACF;IACD,MAAM,SAAS,GAAG,IAAA,oBAAW,EAAC,UAAU,EAAE,WAAW,CAAC,CAAC;IACvD,IAAI,SAAS,EAAE;QAEd,OAAO;YACN,IAAI,EAAE,KAAK;YACX,UAAU,EAAE,IAAA,mCAA2B,EAAC,IAAI,CAAC;SAC7C,CAAC;KACF;IAED,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,IAAA,mCAA2B,EAAC,IAAI,CAAC,EAAE,CAAC;AACtE,CAAC,CAAC;AA9DW,QAAA,iBAAiB,qBA8D5B;AAEF,MAAM,YAAY,GAAG,MAAM,CAAC;AAM5B,MAAM,YAAY,GAAG,CAAC,KAAa,EAAU,EAAE;IAC9C,OAAO,KAAK,CAAC,OAAO,CAAC,YAAY,EAAE,GAAG,CAAC,CAAC;AACzC,CAAC,CAAC;AAOK,MAAM,2BAA2B,GAAG,CAAC,IAAY,EAAmB,EAAE;IAC5E,MAAM,MAAM,GAAG,IAAA,uCAAmB,EAAC,IAAI,CAAC,CAAC;IAEzC,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;QAC3B,IAAI,KAAK,CAAC,IAAI,KAAK,MAAM,EAAE;YAC1B,KAAK,CAAC,IAAI,GAAG,YAAY,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACtC;aAAM;YACN,IAAA,cAAK,EAAC,KAAK,CAAC,MAAM,EAAE;gBACnB,YAAY,CAAC,IAAI;oBAChB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACpB,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,QAAQ,EAAE;wBACxC,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;qBAChD;gBACF,CAAC;gBACD,kBAAkB,CAAC,IAAI;oBACtB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACpB,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBACjD,CAAC;gBACD,oBAAoB,CAAC,IAAI;oBACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;oBACpB,IAAI,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,IAAI,EAAE;wBACpC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;qBAC9D;oBACD,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;gBACzD,CAAC;aACD,CAAC,CAAC;YACH,KAAK,CAAC,IAAI,GAAG,IAAA,cAAK,EAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC;SACtC;KACD;IAED,OAAO;QACN,KAAK,EAAE,IAAA,mCAAc,EAAC,MAAM,CAAC;QAC7B,SAAS,EAAE,qDAAqD;KAChE,CAAC;AACH,CAAC,CAAC;AAlCW,QAAA,2BAA2B,+BAkCtC"}