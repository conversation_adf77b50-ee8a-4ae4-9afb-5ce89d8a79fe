{"name": "clean-stack", "version": "3.0.1", "description": "Clean up error stack traces", "license": "MIT", "repository": "sindresorhus/clean-stack", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["clean", "stack", "trace", "traces", "error", "electron"], "dependencies": {"escape-string-regexp": "4.0.0"}, "devDependencies": {"ava": "^2.4.0", "tsd": "^0.11.0", "xo": "^0.32.0"}, "browser": {"os": false}}