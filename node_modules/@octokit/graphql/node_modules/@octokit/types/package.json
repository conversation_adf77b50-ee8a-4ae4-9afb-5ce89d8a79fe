{"name": "@octokit/types", "version": "14.1.0", "publishConfig": {"access": "public", "provenance": true}, "description": "Shared TypeScript definitions for Octokit projects", "dependencies": {"@octokit/openapi-types": "^25.1.0"}, "repository": "github:octokit/types.ts", "keywords": ["github", "api", "sdk", "toolkit", "typescript"], "author": "<PERSON> (https://twitter.com/gr2m)", "license": "MIT", "devDependencies": {"@octokit/tsconfig": "^4.0.0", "github-openapi-graphql-query": "^5.0.0", "handlebars": "^4.7.6", "npm-run-all2": "^8.0.0", "prettier": "^3.0.0", "semantic-release": "^24.0.0", "semantic-release-plugin-update-version-in-files": "^2.0.0", "sort-keys": "^5.0.0", "typedoc": "^0.28.0", "typescript": "^5.0.0"}, "octokit": {"openapi-version": "19.1.0"}, "files": ["dist-types/**"], "types": "dist-types/index.d.ts", "sideEffects": false}