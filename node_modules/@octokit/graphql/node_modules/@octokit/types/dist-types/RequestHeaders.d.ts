export type RequestHeaders = {
    /**
     * Avoid setting `headers.accept`, use `mediaType.{format|previews}` option instead.
     */
    accept?: string;
    /**
     * Use `authorization` to send authenticated request, remember `token ` / `bearer ` prefixes. Example: `token 1234567890abcdef1234567890abcdef12345678`
     */
    authorization?: string;
    /**
     * `user-agent` is set do a default and can be overwritten as needed.
     */
    "user-agent"?: string;
    [header: string]: string | number | undefined;
};
