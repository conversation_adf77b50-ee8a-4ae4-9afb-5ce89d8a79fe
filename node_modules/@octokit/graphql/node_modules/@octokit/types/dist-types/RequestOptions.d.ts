import type { RequestHeaders } from "./RequestHeaders.js";
import type { RequestMethod } from "./RequestMethod.js";
import type { RequestRequestOptions } from "./RequestRequestOptions.js";
import type { Url } from "./Url.js";
/**
 * Generic request options as they are returned by the `endpoint()` method
 */
export type RequestOptions = {
    method: RequestMethod;
    url: Url;
    headers: RequestHeaders;
    body?: any;
    request?: RequestRequestOptions;
};
