{"version": 3, "sources": ["index.js"], "names": ["Object", "defineProperty", "exports", "value", "join", "tokens", "start", "delimiter", "postDelimiter", "tag", "postTag", "type", "postType", "name", "postName", "description", "end", "lineEnd", "getStringifier", "block", "source", "map", "default"], "mappings": "AAAA;;AACAA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,EAAAA,KAAK,EAAE;AAAT,CAA7C;;AACA,SAASC,IAAT,CAAcC,MAAd,EAAsB;AAClB,SAAQA,MAAM,CAACC,KAAP,GACJD,MAAM,CAACE,SADH,GAEJF,MAAM,CAACG,aAFH,GAGJH,MAAM,CAACI,GAHH,GAIJJ,MAAM,CAACK,OAJH,GAKJL,MAAM,CAACM,IALH,GAMJN,MAAM,CAACO,QANH,GAOJP,MAAM,CAACQ,IAPH,GAQJR,MAAM,CAACS,QARH,GASJT,MAAM,CAACU,WATH,GAUJV,MAAM,CAACW,GAVH,GAWJX,MAAM,CAACY,OAXX;AAYH;;AACD,SAASC,cAAT,GAA0B;AACtB,SAAQC,KAAD,IAAWA,KAAK,CAACC,MAAN,CAAaC,GAAb,CAAiB,CAAC;AAAEhB,IAAAA;AAAF,GAAD,KAAgBD,IAAI,CAACC,MAAD,CAArC,EAA+CD,IAA/C,CAAoD,IAApD,CAAlB;AACH;;AACDF,OAAO,CAACoB,OAAR,GAAkBJ,cAAlB", "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nfunction join(tokens) {\n    return (tokens.start +\n        tokens.delimiter +\n        tokens.postDelimiter +\n        tokens.tag +\n        tokens.postTag +\n        tokens.type +\n        tokens.postType +\n        tokens.name +\n        tokens.postName +\n        tokens.description +\n        tokens.end +\n        tokens.lineEnd);\n}\nfunction getStringifier() {\n    return (block) => block.source.map(({ tokens }) => join(tokens)).join('\\n');\n}\nexports.default = getStringifier;\n"], "file": "index.cjs"}