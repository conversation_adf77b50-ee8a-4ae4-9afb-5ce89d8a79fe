{"version": 3, "sources": ["index.js"], "names": ["Object", "defineProperty", "exports", "value", "primitives_js_1", "require", "util_js_1", "block_parser_js_1", "source_parser_js_1", "spec_parser_js_1", "tag_js_1", "type_js_1", "name_js_1", "description_js_1", "<PERSON><PERSON><PERSON><PERSON>", "startLine", "fence", "spacing", "markers", "Markers", "tokenizers", "default", "Error", "parseSource", "parseBlock", "parseSpec", "joinDescription", "<PERSON><PERSON><PERSON><PERSON>", "source", "blocks", "line", "splitLines", "lines", "sections", "specs", "slice", "map", "push", "description", "tags", "problems", "reduce", "acc", "spec", "concat"], "mappings": "AAAA;;AACAA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,EAAAA,KAAK,EAAE;AAAT,CAA7C;;AACA,MAAMC,eAAe,GAAGC,OAAH,qBAArB;;AACA,MAAMC,SAAS,GAAGD,OAAH,eAAf;;AACA,MAAME,iBAAiB,GAAGF,OAAH,sBAAvB;;AACA,MAAMG,kBAAkB,GAAGH,OAAH,uBAAxB;;AACA,MAAMI,gBAAgB,GAAGJ,OAAH,qBAAtB;;AACA,MAAMK,QAAQ,GAAGL,OAAH,wBAAd;;AACA,MAAMM,SAAS,GAAGN,OAAH,yBAAf;;AACA,MAAMO,SAAS,GAAGP,OAAH,yBAAf;;AACA,MAAMQ,gBAAgB,GAAGR,OAAH,gCAAtB;;AACA,SAASS,SAAT,CAAmB;AAAEC,EAAAA,SAAS,GAAG,CAAd;AAAiBC,EAAAA,KAAK,GAAG,KAAzB;AAAgCC,EAAAA,OAAO,GAAG,SAA1C;AAAqDC,EAAAA,OAAO,GAAGd,eAAe,CAACe,OAA/E;AAAwFC,EAAAA,UAAU,GAAG,CACpH,CAAC,GAAGV,QAAQ,CAACW,OAAb,GADoH,EAEpH,CAAC,GAAGV,SAAS,CAACU,OAAd,EAAuBJ,OAAvB,CAFoH,EAGpH,CAAC,GAAGL,SAAS,CAACS,OAAd,GAHoH,EAIpH,CAAC,GAAGR,gBAAgB,CAACQ,OAArB,EAA8BJ,OAA9B,CAJoH;AAArG,IAKZ,EALP,EAKW;AACP,MAAIF,SAAS,GAAG,CAAZ,IAAiBA,SAAS,GAAG,CAAZ,GAAgB,CAArC,EACI,MAAM,IAAIO,KAAJ,CAAU,mBAAV,CAAN;AACJ,QAAMC,WAAW,GAAG,CAAC,GAAGf,kBAAkB,CAACa,OAAvB,EAAgC;AAAEN,IAAAA,SAAF;AAAaG,IAAAA;AAAb,GAAhC,CAApB;AACA,QAAMM,UAAU,GAAG,CAAC,GAAGjB,iBAAiB,CAACc,OAAtB,EAA+B;AAAEL,IAAAA;AAAF,GAA/B,CAAnB;AACA,QAAMS,SAAS,GAAG,CAAC,GAAGhB,gBAAgB,CAACY,OAArB,EAA8B;AAAED,IAAAA;AAAF,GAA9B,CAAlB;AACA,QAAMM,eAAe,GAAG,CAAC,GAAGb,gBAAgB,CAACc,SAArB,EAAgCV,OAAhC,CAAxB;AACA,SAAO,UAAUW,MAAV,EAAkB;AACrB,UAAMC,MAAM,GAAG,EAAf;;AACA,SAAK,MAAMC,IAAX,IAAmB,CAAC,GAAGxB,SAAS,CAACyB,UAAd,EAA0BH,MAA1B,CAAnB,EAAsD;AAClD,YAAMI,KAAK,GAAGT,WAAW,CAACO,IAAD,CAAzB;AACA,UAAIE,KAAK,KAAK,IAAd,EACI;AACJ,YAAMC,QAAQ,GAAGT,UAAU,CAACQ,KAAD,CAA3B;AACA,YAAME,KAAK,GAAGD,QAAQ,CAACE,KAAT,CAAe,CAAf,EAAkBC,GAAlB,CAAsBX,SAAtB,CAAd;AACAI,MAAAA,MAAM,CAACQ,IAAP,CAAY;AACRC,QAAAA,WAAW,EAAEZ,eAAe,CAACO,QAAQ,CAAC,CAAD,CAAT,EAAcf,OAAd,CADpB;AAERqB,QAAAA,IAAI,EAAEL,KAFE;AAGRN,QAAAA,MAAM,EAAEI,KAHA;AAIRQ,QAAAA,QAAQ,EAAEN,KAAK,CAACO,MAAN,CAAa,CAACC,GAAD,EAAMC,IAAN,KAAeD,GAAG,CAACE,MAAJ,CAAWD,IAAI,CAACH,QAAhB,CAA5B,EAAuD,EAAvD;AAJF,OAAZ;AAMH;;AACD,WAAOX,MAAP;AACH,GAhBD;AAiBH;;AACD3B,OAAO,CAACmB,OAAR,GAAkBP,SAAlB", "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst primitives_js_1 = require(\"../primitives.js\");\nconst util_js_1 = require(\"../util.js\");\nconst block_parser_js_1 = require(\"./block-parser.js\");\nconst source_parser_js_1 = require(\"./source-parser.js\");\nconst spec_parser_js_1 = require(\"./spec-parser.js\");\nconst tag_js_1 = require(\"./tokenizers/tag.js\");\nconst type_js_1 = require(\"./tokenizers/type.js\");\nconst name_js_1 = require(\"./tokenizers/name.js\");\nconst description_js_1 = require(\"./tokenizers/description.js\");\nfunction getParser({ startLine = 0, fence = '```', spacing = 'compact', markers = primitives_js_1.Markers, tokenizers = [\n    (0, tag_js_1.default)(),\n    (0, type_js_1.default)(spacing),\n    (0, name_js_1.default)(),\n    (0, description_js_1.default)(spacing),\n], } = {}) {\n    if (startLine < 0 || startLine % 1 > 0)\n        throw new Error('Invalid startLine');\n    const parseSource = (0, source_parser_js_1.default)({ startLine, markers });\n    const parseBlock = (0, block_parser_js_1.default)({ fence });\n    const parseSpec = (0, spec_parser_js_1.default)({ tokenizers });\n    const joinDescription = (0, description_js_1.getJoiner)(spacing);\n    return function (source) {\n        const blocks = [];\n        for (const line of (0, util_js_1.splitLines)(source)) {\n            const lines = parseSource(line);\n            if (lines === null)\n                continue;\n            const sections = parseBlock(lines);\n            const specs = sections.slice(1).map(parseSpec);\n            blocks.push({\n                description: joinDescription(sections[0], markers),\n                tags: specs,\n                source: lines,\n                problems: specs.reduce((acc, spec) => acc.concat(spec.problems), []),\n            });\n        }\n        return blocks;\n    };\n}\nexports.default = getParser;\n"], "file": "index.cjs"}