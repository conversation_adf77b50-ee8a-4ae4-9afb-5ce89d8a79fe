{"version": 3, "sources": ["spec-parser.js"], "names": ["Object", "defineProperty", "exports", "value", "util_js_1", "require", "<PERSON><PERSON><PERSON><PERSON>", "tokenizers", "parseSpec", "source", "_a", "spec", "seedSpec", "tokenize", "problems", "length", "critical", "default"], "mappings": "AAAA;;AACAA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,EAAAA,KAAK,EAAE;AAAT,CAA7C;;AACA,MAAMC,SAAS,GAAGC,OAAH,eAAf;;AACA,SAASC,SAAT,CAAmB;AAAEC,EAAAA;AAAF,CAAnB,EAAmC;AAC/B,SAAO,SAASC,SAAT,CAAmBC,MAAnB,EAA2B;AAC9B,QAAIC,EAAJ;;AACA,QAAIC,IAAI,GAAG,CAAC,GAAGP,SAAS,CAACQ,QAAd,EAAwB;AAAEH,MAAAA;AAAF,KAAxB,CAAX;;AACA,SAAK,MAAMI,QAAX,IAAuBN,UAAvB,EAAmC;AAC/BI,MAAAA,IAAI,GAAGE,QAAQ,CAACF,IAAD,CAAf;AACA,UAAI,CAACD,EAAE,GAAGC,IAAI,CAACG,QAAL,CAAcH,IAAI,CAACG,QAAL,CAAcC,MAAd,GAAuB,CAArC,CAAN,MAAmD,IAAnD,IAA2DL,EAAE,KAAK,KAAK,CAAvE,GAA2E,KAAK,CAAhF,GAAoFA,EAAE,CAACM,QAA3F,EACI;AACP;;AACD,WAAOL,IAAP;AACH,GATD;AAUH;;AACDT,OAAO,CAACe,OAAR,GAAkBX,SAAlB", "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\nconst util_js_1 = require(\"../util.js\");\nfunction getParser({ tokenizers }) {\n    return function parseSpec(source) {\n        var _a;\n        let spec = (0, util_js_1.seedSpec)({ source });\n        for (const tokenize of tokenizers) {\n            spec = tokenize(spec);\n            if ((_a = spec.problems[spec.problems.length - 1]) === null || _a === void 0 ? void 0 : _a.critical)\n                break;\n        }\n        return spec;\n    };\n}\nexports.default = getParser;\n"], "file": "spec-parser.cjs"}