{"version": 3, "sources": ["tag.js"], "names": ["Object", "defineProperty", "exports", "value", "tagTokenizer", "spec", "tokens", "source", "match", "description", "problems", "push", "code", "message", "line", "number", "critical", "tag", "postTag", "slice", "length", "default"], "mappings": "AAAA;;AACAA,MAAM,CAACC,cAAP,CAAsBC,OAAtB,EAA+B,YAA/B,EAA6C;AAAEC,EAAAA,KAAK,EAAE;AAAT,CAA7C;AACA;AACA;AACA;AACA;;AACA,SAASC,YAAT,GAAwB;AACpB,SAAQC,IAAD,IAAU;AACb,UAAM;AAAEC,MAAAA;AAAF,QAAaD,IAAI,CAACE,MAAL,CAAY,CAAZ,CAAnB;AACA,UAAMC,KAAK,GAAGF,MAAM,CAACG,WAAP,CAAmBD,KAAnB,CAAyB,kBAAzB,CAAd;;AACA,QAAIA,KAAK,KAAK,IAAd,EAAoB;AAChBH,MAAAA,IAAI,CAACK,QAAL,CAAcC,IAAd,CAAmB;AACfC,QAAAA,IAAI,EAAE,iBADS;AAEfC,QAAAA,OAAO,EAAE,kCAFM;AAGfC,QAAAA,IAAI,EAAET,IAAI,CAACE,MAAL,CAAY,CAAZ,EAAeQ,MAHN;AAIfC,QAAAA,QAAQ,EAAE;AAJK,OAAnB;AAMA,aAAOX,IAAP;AACH;;AACDC,IAAAA,MAAM,CAACW,GAAP,GAAaT,KAAK,CAAC,CAAD,CAAlB;AACAF,IAAAA,MAAM,CAACY,OAAP,GAAiBV,KAAK,CAAC,CAAD,CAAtB;AACAF,IAAAA,MAAM,CAACG,WAAP,GAAqBH,MAAM,CAACG,WAAP,CAAmBU,KAAnB,CAAyBX,KAAK,CAAC,CAAD,CAAL,CAASY,MAAlC,CAArB;AACAf,IAAAA,IAAI,CAACY,GAAL,GAAWT,KAAK,CAAC,CAAD,CAAhB;AACA,WAAOH,IAAP;AACH,GAjBD;AAkBH;;AACDH,OAAO,CAACmB,OAAR,GAAkBjB,YAAlB", "sourcesContent": ["\"use strict\";\nObject.defineProperty(exports, \"__esModule\", { value: true });\n/**\n * Splits the `@prefix` from remaining `Spec.lines[].token.description` into the `tag` token,\n * and populates `spec.tag`\n */\nfunction tagTokenizer() {\n    return (spec) => {\n        const { tokens } = spec.source[0];\n        const match = tokens.description.match(/\\s*(@(\\S+))(\\s*)/);\n        if (match === null) {\n            spec.problems.push({\n                code: 'spec:tag:prefix',\n                message: 'tag should start with \"@\" symbol',\n                line: spec.source[0].number,\n                critical: true,\n            });\n            return spec;\n        }\n        tokens.tag = match[1];\n        tokens.postTag = match[3];\n        tokens.description = tokens.description.slice(match[0].length);\n        spec.tag = match[2];\n        return spec;\n    };\n}\nexports.default = tagTokenizer;\n"], "file": "tag.cjs"}