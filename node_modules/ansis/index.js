let{defineProperty:e,setPrototypeOf:t,create:r,keys:n}=Object,l="",{round:s,max:i}=Math,o=e=>{let[,t]=/([a-f\d]{3,6})/i.exec(e)||[],r=t?t.length:0;if(3===r)t=t[0]+t[0]+t[1]+t[1]+t[2]+t[2];else if(6^r)return[0,0,0];let n=parseInt(t,16);return[n>>16&255,n>>8&255,255&n]},a=(e,t,r)=>e===t&&t===r?e<8?16:e>248?231:s((e-8)/247*24)+232:16+36*s(e/51)+6*s(t/51)+s(r/51),c=e=>{let t,r,n,l,o;return e<8?30+e:e<16?e-8+90:(e>=232?t=r=n=(10*(e-232)+8)/255:(o=(e-=16)%36,t=(e/36|0)/5,r=(o/6|0)/5,n=o%6/5),l=2*i(t,r,n),l?30+(s(n)<<2|s(r)<<1|s(t))+(2^l?0:60):30)},u=(()=>{let e=e=>i.some((t=>e.test(t))),t=globalThis,r=t.Deno,l=!!r,s=t.process||r||{},i=s.argv||s.args||[],o=s.env||{},a=-1;if(l)try{o=o.toObject()}catch(e){a=0}let c=!!o.PM2_HOME&&!!o.pm_id||o.NEXT_RUNTIME?.includes("edge")||(l?r.isatty(1):!!s.stdout?.isTTY),u="FORCE_COLOR",p=o[u],g=parseInt(p),d=isNaN(g)?"false"===p?0:-1:g,f=u in o&&d||e(/^-{1,2}color=?(true|always)?$/);return f&&(a=d),a<0&&(a=((e,t,r)=>{let l=e.TERM,s=","+n(e).join(",");return{"24bit":3,truecolor:3,ansi256:2,ansi:1}[e.COLORTERM]||(e.TF_BUILD?1:/,TEAMCI/.test(s)?2:e.CI?/,GIT(HUB|EA)/.test(s)?3:1:!t||/-mono|dumb/i.test(l)?0:r||/term-(kit|dir)/.test(l)?3:/-256/.test(l)?2:/scr|xterm|tty|ansi|color|[nm]ux|vt|cyg/.test(l)?1:3)})(o,c,"win32"===(l?r.build.os:s.platform))),!d||o.NO_COLOR||e(/^-{1,2}(no-color|color=(false|never))$/)?0:f&&!a||t.window?.chrome?3:a})(),p=u>0,g={open:l,close:l},d=p?(e,t)=>({open:`[${e}m`,close:`[${t}m`}):()=>g,f=39,b=49,_=(e,t)=>(r,n,l)=>d(((e,t,r)=>c(a(e,t,r)))(r,n,l)+e,t),m=e=>(t,r,n)=>e(a(t,r,n)),y=e=>t=>e(...o(t)),h=(e,t,r)=>d(`38;2;${e};${t};${r}`,f),O=(e,t,r)=>d(`48;2;${e};${t};${r}`,b),$=e=>d(`38;5;${e}`,f),x=e=>d(`48;5;${e}`,b);2===u?(h=m($),O=m(x)):1===u&&(h=_(0,f),O=_(10,b),$=e=>d(c(e),f),x=e=>d(c(e)+10,b));let T,w={ansi256:$,bgAnsi256:x,fg:$,bg:x,rgb:h,bgRgb:O,hex:y(h),bgHex:y(O),visible:g,reset:d(0,0),bold:d(1,22),dim:d(2,22),italic:d(3,23),underline:d(4,24),inverse:d(7,27),hidden:d(8,28)},R="Bright",E=30;"black,red,green,yellow,blue,magenta,cyan,white".split(",").map((e=>{T="bg"+e[0].toUpperCase()+e.slice(1),w[e]=d(E,f),w[e+R]=d(60+E,f),w[T]=d(E+10,b),w[T+R]=d(70+E++,b)})),w.grey=w.gray=d(90,f),w.bgGrey=w.bgGray=d(100,b),w.strikethrough=w.strike=d(9,29);let v,C={},I=({_p:e},{open:r,close:n})=>{let s=(e,...t)=>{if(!e){if(r&&r===n)return r;if(null==e||l===e)return l}let i=e.raw?String.raw(e,...t).replace(/\\n/g,"\n"):l+e,o=s._p,{_a:a,_b:c}=o;if(i.includes(""))for(;o;){let e,t=o.close,r=o.open,n=t.length,s=l,a=0;if(n){for(;~(e=i.indexOf(t,a));a=e+n)s+=i.slice(a,e)+r;i=s+i.slice(a)}o=o._p}return i.includes("\n")&&(i=i.replace(/(\r?\n)/g,c+"$1"+a)),a+i+c},i=r,o=n;return e&&(i=e._a+r,o=n+e._b),t(s,v),s._p={open:r,close:n,_a:i,_b:o,_p:e},s.open=i,s.close=o,s};const M=function(){let n={Ansis:M,isSupported:()=>p,strip:e=>e.replace(/[][[()#;?]*(?:[0-9]{1,4}(?:;[0-9]{0,4})*)?[0-9A-ORZcf-nqry=><]/g,l),extend(l){for(let t in l){let r=l[t],n=(typeof r)[0],s="s"===n?h(...o(r)):r;C[t]="f"===n?{get(){return(...e)=>I(this,r(...e))}}:{get(){let r=I(this,s);return e(this,t,{value:r}),r}}}return v=r({},C),t(n,v),n}};return n.extend(w)},k=new M;module.exports=k,k.default=k;
