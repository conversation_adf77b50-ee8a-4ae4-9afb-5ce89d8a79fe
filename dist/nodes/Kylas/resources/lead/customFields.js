"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.customFieldsDescription = void 0;
const showOnlyForLeadCreate = {
    operation: ['create'],
    resource: ['lead'],
};
exports.customFieldsDescription = [
    {
        displayName: "Custom Text Fields",
        name: "customTextFields",
        type: "collection",
        placeholder: "Add Custom Text Field",
        default: {},
        displayOptions: {
            show: showOnlyForLeadCreate,
        },
        description: "Add custom text fields for the lead",
        options: [
            {
                displayName: "Field Name or ID",
                name: "fieldName",
                type: "options",
                typeOptions: {
                    loadOptionsMethod: "getLeadCustomFields",
                    loadOptionsMethodParams: {
                        fieldType: "TEXT_FIELD"
                    }
                },
                default: "",
                description: "Choose from the list, or specify an ID using an <a href='https://docs.n8n.io/code/expressions/'>expression</a>"
            },
            {
                displayName: "Field Value",
                name: "fieldValue",
                type: "string",
                default: "",
                description: "Enter the value for this custom field",
                displayOptions: {
                    show: {
                        fieldName: ['!=', '']
                    }
                }
            },
        ],
        routing: {
            send: {
                type: 'body',
                property: '={{$parameter.customTextFields.fieldName}}',
                value: '={{$parameter.customTextFields.fieldValue}}',
            },
        },
    },
    {
        displayName: "Custom Number Fields",
        name: "customNumberFields",
        type: "collection",
        placeholder: "Add Custom Number Field",
        default: {},
        displayOptions: {
            show: showOnlyForLeadCreate,
        },
        description: "Add custom number fields for the lead",
        options: [
            {
                displayName: "Field Name or ID",
                name: "fieldName",
                type: "options",
                typeOptions: {
                    loadOptionsMethod: "getLeadCustomFields",
                    loadOptionsMethodParams: {
                        fieldType: "NUMBER_FIELD"
                    }
                },
                default: "",
                description: "Choose from the list, or specify an ID using an <a href='https://docs.n8n.io/code/expressions/'>expression</a>",
            },
            {
                displayName: "Field Value",
                name: "fieldValue",
                type: "number",
                default: 0,
                description: "Enter the value for this custom field",
                displayOptions: {
                    show: {
                        fieldName: ['!=', '']
                    }
                }
            },
        ],
        routing: {
            send: {
                type: 'body',
                property: '={{$parameter.customNumberFields.fieldName}}',
                value: '={{$parameter.customNumberFields.fieldValue}}',
            },
        },
    },
    {
        displayName: "Custom Date Fields",
        name: "customDateFields",
        type: "collection",
        placeholder: "Add Custom Date Field",
        default: {},
        displayOptions: {
            show: showOnlyForLeadCreate,
        },
        description: "Add custom date fields for the lead",
        options: [
            {
                displayName: "Field Name or ID",
                name: "fieldName",
                type: "options",
                typeOptions: {
                    loadOptionsMethod: "getLeadCustomFields",
                    loadOptionsMethodParams: {
                        fieldType: "DATE_FIELD"
                    }
                },
                default: "",
                description: "Choose from the list, or specify an ID using an <a href=", https: 
            },
            {
                displayName: "Field Value",
                name: "fieldValue",
                type: "dateTime",
                default: "",
                description: "Enter the value for this custom field",
                displayOptions: {
                    show: {
                        fieldName: ['!=', '']
                    }
                }
            },
        ],
        routing: {
            send: {
                type: 'body',
                property: '={{$parameter.customDateFields.fieldName}}',
                value: '={{$parameter.customDateFields.fieldValue}}',
            },
        },
    },
    {
        displayName: "Custom Boolean Fields",
        name: "customBooleanFields",
        type: "collection",
        placeholder: "Add Custom Boolean Field",
        default: {},
        displayOptions: {
            show: showOnlyForLeadCreate,
        },
        description: "Add custom boolean fields for the lead",
        options: [
            {
                displayName: "Field Name or ID",
                name: "fieldName",
                type: "options",
                typeOptions: {
                    loadOptionsMethod: "getLeadCustomFields",
                    loadOptionsMethodParams: {
                        fieldType: "BOOLEAN_FIELD"
                    }
                },
                default: "",
                description: "Select a custom boolean field",
            },
            {
                displayName: "Field Value",
                name: "fieldValue",
                type: "boolean",
                default: false,
                description: "Enter the value for this custom field",
                displayOptions: {
                    show: {
                        fieldName: ['!=', '']
                    }
                }
            },
        ],
        routing: {
            send: {
                type: 'body',
                property: '={{$parameter.customBooleanFields.fieldName}}',
                value: '={{$parameter.customBooleanFields.fieldValue}}',
            },
        },
    },
    {
        displayName: "Custom Picklist Fields",
        name: "customPicklistFields",
        type: "collection",
        placeholder: "Add Custom Picklist Field",
        default: {},
        displayOptions: {
            show: showOnlyForLeadCreate,
        },
        description: "Add custom picklist fields for the lead",
        options: [
            {
                displayName: "Field Name or ID",
                name: "fieldName",
                type: "options",
                typeOptions: {
                    loadOptionsMethod: "getLeadCustomFields",
                    loadOptionsMethodParams: {
                        fieldType: "PICKLIST"
                    }
                },
                default: "",
                description: "Select a custom picklist field",
            },
            {
                displayName: "Field Value",
                name: "fieldValue",
                type: "options",
                typeOptions: {
                    loadOptionsMethod: "getPicklistValues",
                    loadOptionsDependsOn: ["fieldName"],
                },
                default: "",
                description: "Select a value for this picklist field",
                displayOptions: {
                    show: {
                        fieldName: ['!=', '']
                    }
                }
            },
        ],
        routing: {
            send: {
                type: 'body',
                property: '={{$parameter.customPicklistFields.fieldName}}',
                value: '={{$parameter.customPicklistFields.fieldValue}}',
            },
        },
    },
];
//# sourceMappingURL=customFields.js.map