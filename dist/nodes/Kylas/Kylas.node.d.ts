import { INodeType, INodeTypeDescription, ILoadOptionsFunctions, INodePropertyOptions } from 'n8n-workflow';
export declare class <PERSON><PERSON><PERSON> implements INodeType {
    description: INodeTypeDescription;
    methods: {
        loadOptions: {
            getLeadCustomFields(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]>;
            getPicklistValues(this: ILoadOptionsFunctions): Promise<INodePropertyOptions[]>;
        };
    };
}
