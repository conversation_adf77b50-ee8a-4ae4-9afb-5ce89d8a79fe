"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Kylas = void 0;
const user_1 = require("./resources/user");
const company_1 = require("./resources/company");
const lead_1 = require("./resources/lead");
class Kylas {
    constructor() {
        this.description = {
            displayName: 'Kyla<PERSON>',
            name: 'kyla<PERSON>',
            icon: { light: 'file:kylas.svg', dark: 'file:kylas.dark.svg' },
            group: ['transform'],
            version: 1,
            subtitle: '={{$parameter["operation"] + ": " + $parameter["resource"]}}',
            description: 'Interact with the Kylas API',
            defaults: {
                name: '<PERSON>yla<PERSON>',
            },
            usableAsTool: true,
            inputs: ['main'],
            outputs: ['main'],
            credentials: [{ name: 'kylas<PERSON><PERSON>', required: true }],
            requestDefaults: {
                baseURL: 'https://api-qa.sling-dev.com',
                headers: {
                    Accept: 'application/json',
                    'Content-Type': 'application/json',
                },
            },
            properties: [
                {
                    displayName: 'Resource',
                    name: 'resource',
                    type: 'options',
                    noDataExpression: true,
                    options: [
                        {
                            name: 'User',
                            value: 'user',
                        },
                        {
                            name: 'Company',
                            value: 'company',
                        },
                        {
                            name: 'Lead',
                            value: 'lead',
                        },
                    ],
                    default: 'user',
                },
                ...user_1.userDescription,
                ...company_1.companyDescription,
                ...lead_1.leadDescription
            ],
        };
        this.methods = {
            loadOptions: {
                async getLeadCustomFields() {
                    const requestOptions = {
                        method: 'GET',
                        url: '/v1/layouts/leads/system-fields',
                        qs: {
                            view: 'create'
                        },
                        json: true,
                    };
                    const response = await this.helpers.httpRequestWithAuthentication.call(this, 'kylasApi', requestOptions);
                    const fields = response;
                    const currentNodeParameters = this.getCurrentNodeParameters();
                    const fieldType = currentNodeParameters === null || currentNodeParameters === void 0 ? void 0 : currentNodeParameters.fieldType;
                    let customFields = fields.filter(field => field.active &&
                        !field.standard &&
                        !field.internal);
                    if (fieldType) {
                        customFields = customFields.filter(field => field.type === fieldType);
                    }
                    return customFields.map(field => ({
                        name: field.displayName,
                        value: field.name,
                        description: `Type: ${field.type}${field.required ? ' (Required)' : ''}`,
                    }));
                },
                async getPicklistValues() {
                    var _a;
                    const currentNodeParameters = this.getCurrentNodeParameters();
                    const fieldName = currentNodeParameters === null || currentNodeParameters === void 0 ? void 0 : currentNodeParameters.fieldName;
                    if (!fieldName) {
                        return [];
                    }
                    const fieldsRequestOptions = {
                        method: 'GET',
                        url: '/v1/layouts/leads/system-fields',
                        qs: {
                            view: 'create'
                        },
                        json: true,
                    };
                    const fieldsResponse = await this.helpers.httpRequestWithAuthentication.call(this, 'kylasApi', fieldsRequestOptions);
                    const fields = fieldsResponse;
                    const field = fields.find(f => f.name === fieldName);
                    if (!((_a = field === null || field === void 0 ? void 0 : field.picklist) === null || _a === void 0 ? void 0 : _a.id)) {
                        return [];
                    }
                    const picklistRequestOptions = {
                        method: 'GET',
                        url: `/v1/picklists/${field.picklist.id}/values`,
                        json: true,
                    };
                    const picklistResponse = await this.helpers.httpRequestWithAuthentication.call(this, 'kylasApi', picklistRequestOptions);
                    const picklistValues = picklistResponse;
                    return picklistValues
                        .filter(value => value.active)
                        .map(value => ({
                        name: value.displayName,
                        value: value.value,
                    }));
                },
            },
        };
    }
}
exports.Kylas = Kylas;
//# sourceMappingURL=Kylas.node.js.map