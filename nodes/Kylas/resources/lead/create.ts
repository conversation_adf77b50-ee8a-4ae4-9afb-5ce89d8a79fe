import type { INodeProperties } from 'n8n-workflow';
import { customFieldsDescription } from './customFields';

const showOnlyForLeadCreate = {
    operation: ['create'],
    resource: ['lead'],
};

export const leadCreateDescription: INodeProperties[] = [
    {
        displayName: "First Name",
        name: "firstName",
        description: 'First name of the lead',
        type: 'string',
        required: true,
        default: '',
        displayOptions: {
            show: showOnlyForLeadCreate,
        },
        routing: {
            send: {
                type: 'body',
                property: 'firstName',
            },
        },
    },
    {
        displayName: "Last Name",
        name: "lastName",
        description: 'Last name of the lead',
        type: 'string',
        required: true,
        default: '',
        displayOptions: {
            show: showOnlyForLeadCreate,
        },
        routing: {
            send: {
                type: 'body',
                property: 'lastName',
            },
        },
    },
    {
        displayName: "Additional Field",
        name: "additionalFields",
        type: "collection",
        placeholder: "Add Field",
        default: {},
         displayOptions: {
            show: showOnlyForLeadCreate,
        },
        options: [
            {
                displayName: "Address",
                name: "address",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'address',
                    },
                },
            },
            {
                displayName: "City",
                name: "city",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'city',
                    },
                },
            },
            {
                displayName: "Company Address",
                name: "companyAddress",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'companyAddress',
                    },
                },
            },
            {
                displayName: "Company Annual Revenue",
                name: "companyAnnualRevenue",
                type: "number",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'companyAnnualRevenue',
                    },
                },
            },
            {
                displayName: "Company City",
                name: "companyCity",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'companyCity',
                    },
                },
            },
            {
                displayName: "Company Name",
                name: "companyName",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'companyName',
                    },
                },
            },
            {
                displayName: "Company Phones",
                name: "companyPhones",
                type: "fixedCollection",
                default: {
                    displayName: "Company Phone",
                    name: "value",
                    type: "string",
                    default: "",
                    description: "Phone number without country code",
                },
                description: "Add phone numbers",
                typeOptions: {
                    multipleValues: true,
                },
                routing: {
                    send: {
                        type: 'body',
                        property: 'companyPhones',
                        value: '={{$parameter.companyPhones.companyPhone}}',
                    },
                },
                options: [
                    {
                        displayName: "Company Number",
                        name: "companyPhone",
                        values: [
                            {
                                displayName: "Company Phone",
                                name: "value",
                                type: "string",
                                default: "",
                                description: "Phone number without country code",
                            },
                            {
                                displayName: "Country Code",
                                name: "code",
                                type: "string",
                                default: "IN",
                                description: "Country code (e.g., IN, US)",
                            },
                            {
                                displayName: "Dial Code",
                                name: "dialCode",
                                type: "string",
                                default: "+91",
                                description: "Dial code with + prefix (e.g., +91, +1)",
                            },
                            {
                                displayName: "Primary",
                                name: "primary",
                                type: "boolean",
                                default: false,
                                description: "Whether this is the primary phone number",
                            },
                            {
                                displayName: "Type",
                                name: "type",
                                type: "options",
                                options: [
                                    { name: "Mobile", value: "MOBILE" },
                                    { name: "Home", value: "HOME" },
                                    { name: "Work", value: "WORK" },
                                ],
                                default: "MOBILE",
                                description: "Type of phone number",
                            },
                        ],
                    },
                ],
            },
            {
                displayName: "Company State",
                name: "companyState",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'companyState',
                    },
                },
            },
            {
                displayName: "Company Website",
                name: "companyWebsite",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'companyWebsite',
                    },
                },
            },
            {
                displayName: "Company Zipcode",
                name: "companyZipcode",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'companyZipcode',
                    },
                },
            },
            {
                displayName: "Department",
                name: "department",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'department',
                    },
                },
            },
            {
                displayName: "Designation",
                name: "designation",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'designation',
                    },
                },
            },
            {
                displayName: "Do Not Disturb",
                name: "dnd",
                type: "boolean",
                default: false,
                routing: {
                    send: {
                        type: 'body',
                        property: 'dnd',
                    },
                },
            },
            {
                displayName: "Emails",
                name: "emails",
                type: "fixedCollection",
                default: {
                    displayName: "Email ID",
                    name: "value",
                    type: "string",
                    default: "",
                },
                description: "Add emails",
                typeOptions: {
                    multipleValues: true,
                },
                routing: {
                    send: {
                        type: 'body',
                        property: 'emails',
                        value: '={{$parameter.emails.email}}',
                    },
                },
                options: [
                    {
                        displayName: "Email",
                        name: "email",
                        values: [
                            { displayName: "Email ID", name: "value", type: "string", default: "" },
                            { displayName: "Primary", name: "primary", type: "boolean", default: false },
                            {
                                displayName: "Type",
                                name: "type",
                                type: "options",
                                options: [
                                    { name: "Office", value: "OFFICE" },
                                    { name: "Personal", value: "PERSONAL" },
                                ],
                                default: "OFFICE",
                                description: "Type of email",
                            },
                        ],
                    },
                ],
            },
            {
                displayName: "Expected Closure On",
                name: "expectedClosureOn",
                type: "dateTime",
                default: "",
                description: "Expected closure date and time (UTC)",
                routing: {
                    send: {
                        type: 'body',
                        property: 'expectedClosureOn',
                    },
                },
            },
            {
                displayName: "Facebook",
                name: "facebook",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'facebook',
                    },
                },
            },
            {
                displayName: "Linked In",
                name: "linkedIn",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'linkedIn',
                    },
                },
            },
            {
                displayName: "Phone Numbers",
                name: "phoneNumbers",
                type: "fixedCollection",
                default: {
                    displayName: "Phone Number",
                    name: "value",
                    type: "string",
                    default: "",
                    description: "Phone number without country code",
                },
                description: "Add phone numbers",
                typeOptions: {
                    multipleValues: true,
                },
                routing: {
                    send: {
                        type: 'body',
                        property: 'phoneNumbers',
                        value: '={{$parameter.phoneNumbers.phoneNumber}}',
                    },
                },
                options: [
                    {
                        displayName: "Phone Number",
                        name: "phoneNumber",
                        values: [
                            {
                                displayName: "Country Code",
                                name: "code",
                                type: "string",
                                default: "IN",
                                description: "Country code (e.g., IN, US)",
                            },
                            {
                                displayName: "Dial Code",
                                name: "dialCode",
                                type: "string",
                                default: "+91",
                                description: "Dial code with + prefix (e.g., +91, +1)",
                            },
                            {
                                displayName: "Phone Number",
                                name: "value",
                                type: "string",
                                default: "",
                                description: "Phone number without country code",
                            },
                            {
                                displayName: "Primary",
                                name: "primary",
                                type: "boolean",
                                default: false,
                                description: "Whether this is the primary phone number",
                            },
                            {
                                displayName: "Type",
                                name: "type",
                                type: "options",
                                options: [
                                    { name: "Mobile", value: "MOBILE" },
                                    { name: "Home", value: "HOME" },
                                    { name: "Work", value: "WORK" },
                                ],
                                default: "MOBILE",
                                description: "Type of phone number",
                            },
                        ]
                        ,
                    },
                ],
            },
            {
                displayName: "Requirement",
                name: "requirementName",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'requirementName',
                    },
                },
            },
            {
                displayName: "State",
                name: "state",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'state',
                    },
                },
            },
            {
                displayName: "Sub Source",
                name: "subSource",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'subSource',
                    },
                },
            },
            {
                displayName: "Twitter",
                name: "twitter",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'twitter',
                    },
                },
            },
            {
                displayName: "UTM Campaign",
                name: "utmCampaign",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'utmCampaign',
                    },
                },
            },
            {
                displayName: "UTM Content",
                name: "utmContent",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'utmContent',
                    },
                },
            },
            {
                displayName: "UTM Medium",
                name: "utmMedium",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'utmMedium',
                    },
                },
            },
            {
                displayName: "UTM Source",
                name: "utmSource",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'utmSource',
                    },
                },
            },
            {
                displayName: "UTM Term",
                name: "utmTerm",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'utmTerm',
                    },
                },
            },
            {
                displayName: "Zipcode",
                name: "zipcode",
                type: "string",
                default: "",
                routing: {
                    send: {
                        type: 'body',
                        property: 'zipcode',
                    },
                },
            },
        ]
    },
    ...customFieldsDescription

];


